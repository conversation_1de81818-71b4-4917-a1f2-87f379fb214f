[package]
name = "actlogica_logs"
version = "0.1.0"
edition = "2021"

[lib]
name = "actlogica_logs"
path = "src/lib.rs"


[dependencies]
rdkafka = { version = "0.37.0", features = ["tokio"] }
tokio = { version = "1", features = ["full"] }
serde = { version = "1.0", features = ["derive"] }
chrono = { version = "0.4", features = ["serde"] }
uuid = { version = "1.3", features = ["serde", "v4"] }
tracing-subscriber = { version = "0.3.19", features = ["json", "fmt"] }
tracing-appender = "0.2.3"
tracing-core = "0.1.33"
tracing = "0.1.41"
serde_json = "1.0.140"
base62 = "2.2.1"
dotenv = "0.15.0"
