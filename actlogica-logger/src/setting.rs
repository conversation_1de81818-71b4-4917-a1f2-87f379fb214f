use crate::kafka::init_kafka_logger;
use crate::utils::setup_log_file;
use serde::de::Error;
use serde_json::{json, Value};
use std::sync::mpsc::Sender;
use std::sync::OnceLock;
use tracing::field::{Field, Visit};
use tracing::{Event, Subscriber};
use tracing_subscriber::fmt::FormattedFields;
use tracing_subscriber::registry::LookupSpan;
use tracing_subscriber::{
    filter::LevelFilter,
    fmt::{format::Writer, FormatEvent, FormatFields},
    layer::SubscriberExt,
};

pub static LOGGER: OnceLock<Sender<String>> = OnceLock::new(); // KafkaLogger-sender

#[derive(Clone)]
pub enum LogOutput {
    StdOut,
    Kafka,
    File(String),
}

/// Initializes tracing-subscriber with custom logging settings.
pub async fn init_logger(
    service_name: &str,
    log_level: LevelFilter,
    output_type: LogOutput,
) -> Result<(), Box<dyn std::error::Error>> {
    // Load env variables...
    dotenv::dotenv().ok();

    // Initialize the subscriber with JSON formatting and custom event formatter
    let fmt_layer = tracing_subscriber::fmt::layer()
        .json()
        .without_time()
        .with_level(true)
        .with_current_span(true)
        .event_format(CustomFormatter {
            service_name: service_name.to_string(),
            output_type: output_type.clone(),
        });

    let subscriber = tracing_subscriber::registry().with(log_level);

    match output_type {
        LogOutput::StdOut => {
            // set tracing subscriber globally
            let fmt_layer = fmt_layer.with_writer(std::io::stdout);
            tracing::subscriber::set_global_default(subscriber.with(fmt_layer))
                .map_err(|err| format!("Failed to set global subscriber: {}", err))?;
        }
        LogOutput::Kafka => {
            let broker = std::env::var("KAFKA_BROKER").expect("kafka-broker-url required in env");
            let topic_name =
                std::env::var("KAFKA_TOPIC_NAME").expect("kafka-topic-name required in env");

            // Initialize the Kafka logger
            init_kafka_logger(&broker, &topic_name).await;

            // set tracing subcriber globally
            tracing::subscriber::set_global_default(subscriber.with(fmt_layer))
                .map_err(|err| format!("Failed to set global subscriber: {}", err))?;
        }
        LogOutput::File(file_path) => {
            // set tracing subcriber globally
            let non_blocking = setup_log_file(&file_path);
            let fmt_layer = fmt_layer.with_writer(non_blocking);
            tracing::subscriber::set_global_default(subscriber.with(fmt_layer))
                .map_err(|err| format!("Failed to set global subscriber: {}", err))?;
        }
    }
    Ok(())
}

/// Custom JSON event formatter for tracing logs
#[derive(Clone)]
struct CustomFormatter {
    service_name: String,
    output_type: LogOutput,
}
impl<S, N> FormatEvent<S, N> for CustomFormatter
where
    S: Subscriber + for<'a> LookupSpan<'a>,
    N: for<'a> FormatFields<'a> + 'static,
{
    fn format_event(
        &self,
        ctx: &tracing_subscriber::fmt::FmtContext<'_, S, N>,
        mut writer: Writer<'_>,
        event: &Event<'_>,
    ) -> std::fmt::Result {
        // Extract structured fields from the event
        let structured_data = process_event(event);

        let mut event_and_module = json!({});

        
        let log = match parse_log_entry(structured_data.clone()) {
            Ok(log) => log,
            Err(_) => {
                // eprintln!("⚠️ Received unstructured log, skipping..");
                return Ok(()); // skip writing this log (or handle it another way)
            }
        };

        if self.output_type == LogOutput::StdOut {
            println!("{} - {} - {}", log.timestamp, log.log_level, log.log_msg);
            return Ok(())
        };

        // Collect span fields into a map and merge with structured_data
        if let Some(scope) = ctx.event_scope() {
            for span in scope.from_root() {
                let ext = span.extensions();
                if let Some(fields) = ext.get::<FormattedFields<N>>() {
                    if !fields.is_empty() {
                        let span_fields: serde_json::Value =
                            serde_json::from_str(&fields.to_string()).unwrap_or_else(|_| json!({}));
                        if let Some(obj) = span_fields.as_object() {
                            if let Some(event_and_module_obj) = event_and_module.as_object_mut() {
                                for (k, v) in obj {
                                    event_and_module_obj.insert(k.clone(), v.clone());
                                }
                            }
                        }
                    }
                }
            }
        }

        let mut log_value: Value = match serde_json::to_value(&log) {
            Ok(log) => log,
            Err(_) => return Ok(()),
        };

        if let Some(event_and_module_obj) = event_and_module.as_object() {
            if let Some(log_obj) = log_value.as_object_mut() {
                for key in ["event_id", "module"] {
                    if let Some(value) = event_and_module_obj.get(key) {
                        log_obj.insert(key.to_string(), value.clone());
                    } else {
                        // eprintln!("Missing field '{}' in log-id: {}", key, log.log_id);
                    }
                }
            }
        };

        let log = match serde_json::to_string(&log_value) {
            Ok(log) => log,
            Err(err) => {
                eprintln!("Failed to serialize log_value: {}", err);
                return Ok(());
            }
        };

        // --------------------- Compose log message as final object -------------------->
        let final_log = serde_json::json!({
            "scope": self.service_name,
            "fields": {
                "message" : log
            },
        });

        // ------------- Serialize it ----------------->
        let final_json = serde_json::to_string(&final_log).unwrap_or_else(|_| "{}".to_string());

        match &self.output_type {
             LogOutput::File(_) => {
                // Write to stdout
                writeln!(writer, "{}", final_json)?;
            }
            LogOutput::Kafka => {
                // Send to Kafka via global logger
                if let Some(logger) = LOGGER.get() {
                    // writeln!(writer, "{}", final_json)?;
                    let _ = logger.send(final_json);
                } else {
                    eprintln!("⚠️ LOGGER not initialized, skipping Kafka send");
                }
            }
        }

        Ok(())
    }
}

struct JsonFieldVisitor {
    values: serde_json::Map<String, serde_json::Value>,
}

impl JsonFieldVisitor {
    fn new() -> Self {
        Self {
            values: serde_json::Map::new(),
        }
    }
}

impl Visit for JsonFieldVisitor {
    fn record_str(&mut self, field: &Field, value: &str) {
        self.values.insert(
            field.name().to_string(),
            serde_json::Value::String(value.to_string()),
        );
    }

    fn record_debug(&mut self, field: &Field, value: &dyn std::fmt::Debug) {
        self.values.insert(
            field.name().to_string(),
            serde_json::json!(format!("{:?}", value)),
        );
    }
}

fn process_event(event: &tracing::Event) -> serde_json::Value {
    let mut visitor = JsonFieldVisitor::new();
    event.record(&mut visitor);

    // Convert collected fields into a JSON object
    serde_json::Value::Object(visitor.values)
}

// Parse log entry into Message struct
fn parse_log_entry(json: Value) -> Result<Message, serde_json::Error> {
    let obj = json
        .as_object()
        .ok_or_else(|| serde_json::Error::custom("Expected JSON object"))?;
    let message_str = obj
        .get("message")
        .and_then(|v| v.as_str())
        .ok_or_else(|| serde_json::Error::custom("Missing or invalid message field"))?;
    serde_json::from_str(message_str).map_err(|err| err)
}

#[derive(Debug, serde::Serialize, serde::Deserialize)]
struct Message {
    timestamp: String,
    log_id: String,
    log_level: String,
    log_type: String,
    user_id: Option<String>,
    user_name: Option<String>,
    log_msg: String,
    action: String,
    metadata: Value,
}
