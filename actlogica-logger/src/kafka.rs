use crate::setting::LOGGER;
use rdkafka::producer::{FutureProducer, FutureRecord};
use rdkafka::util::Timeout;
use std::sync::mpsc::channel;
use std::time::Duration;

use rdkafka::admin::{AdminClient, AdminOptions, NewTopic, TopicReplication};
use rdkafka::client::DefaultClientContext;
use rdkafka::config::ClientConfig;
use rdkafka::types::RDKafkaRespErr;
use tokio;

pub struct KafkaLogger {
    producer: FutureProducer,
    topic: String,
}

/// Initialize the Kafka logger
///
/// This function creates a new channel and assigns the sender part to the
/// global logger. It then spawns a new task that will receive messages from
/// the channel and send them to the specified Kafka topic.
///
pub async fn init_kafka_logger(brokers: &str, topic: &str) {
    // Create a channel with a buffer size of 10000
    let (tx, rx) = channel::<String>();

    // create kafka-topic if not exists
    KafkaLogger::create_kafka_topic(brokers, topic).await;

    // Assign the sender to the global logger
    let kafka_logger = KafkaLogger::new(brokers, topic);
    let _ = LOGGER.set(tx);

    // Spawn a task to send received logs to Kafka
    tokio::spawn(async move {
        while let Ok(log) = rx.recv() {
            kafka_logger.send_log(&log).await;
        }
        eprintln!("⚠️ Channel closed, exiting Kafka logger task");
    });
}

impl KafkaLogger {
    pub fn new(brokers: &str, topic: &str) -> Self {
        let producer = ClientConfig::new()
            .set("bootstrap.servers", brokers)
            .set("message.timeout.ms", "5000")
            .create()
            .expect("Failed to create Kafka client");

        Self {
            producer,
            topic: topic.to_string(),
        }
    }

    pub async fn create_kafka_topic(brokers: &str, topic_name: &str) {
        // Create AdminClient
        let admin_client: AdminClient<DefaultClientContext> = ClientConfig::new()
            .set("bootstrap.servers", brokers)
            .create()
            .expect("Admin client creation failed");

        // Define new topic
        let new_topic = NewTopic::new(
            topic_name,
            1,                          // partitions
            TopicReplication::Fixed(1), // replication factor
        );

        // Try to create the topic
        match admin_client
            .create_topics(&[new_topic], &AdminOptions::new())
            .await
        {
            Ok(responses) => {
                for response in responses {
                    match response {
                        Ok(topic) => println!("✅ Created topic: {}", topic),
                        Err((topic, err)) => {
                            if err == RDKafkaRespErr::RD_KAFKA_RESP_ERR_TOPIC_ALREADY_EXISTS.into()
                            {
                                println!("⚠️ Topic '{}' already exists.", topic);
                            } else {
                                eprintln!("❌ Failed to create topic '{}': {:?}", topic, err);
                            }
                        }
                    }
                }
            }
            Err(e) => eprintln!("❌ Admin request failed: {:?}", e),
        }
    }

    pub async fn send_log(&self, log: &str) {
        let record: FutureRecord<'_, (), [u8]> =
            FutureRecord::to(&self.topic).payload(log.as_bytes());

        if let Err((e, _)) = self
            .producer
            .send(record, Timeout::After(Duration::from_secs(10)))
            .await
        {
            eprintln!("Failed to send log to Kafka: {}", e);
        }

        println!("Log sent successfully");
    }
}
