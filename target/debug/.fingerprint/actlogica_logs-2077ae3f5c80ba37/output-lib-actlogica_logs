{"$message_type":"diagnostic","message":"unexpected `,` in pattern","code":null,"level":"error","spans":[{"file_name":"actlogica-logger/src/setting.rs","byte_start":6165,"byte_end":6166,"line_start":172,"line_end":172,"column_start":14,"column_end":15,"is_primary":true,"text":[{"text":"            },","highlight_start":14,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"try adding parentheses to match on a tuple...","code":null,"level":"help","spans":[{"file_name":"actlogica-logger/src/setting.rs","byte_start":6132,"byte_end":6132,"line_start":171,"line_end":171,"column_start":13,"column_end":13,"is_primary":true,"text":[{"text":"            LogOutput::StdOut {","highlight_start":13,"highlight_end":13}],"label":null,"suggested_replacement":"(","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"actlogica-logger/src/setting.rs","byte_start":6197,"byte_end":6197,"line_start":173,"line_end":173,"column_start":31,"column_end":31,"is_primary":true,"text":[{"text":"            LogOutput::File(_) => {","highlight_start":31,"highlight_end":31}],"label":null,"suggested_replacement":")","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null},{"message":"...or a vertical bar to match on multiple alternatives","code":null,"level":"help","spans":[{"file_name":"actlogica-logger/src/setting.rs","byte_start":6132,"byte_end":6197,"line_start":171,"line_end":173,"column_start":13,"column_end":31,"is_primary":true,"text":[{"text":"            LogOutput::StdOut {","highlight_start":13,"highlight_end":32},{"text":"            },","highlight_start":1,"highlight_end":15},{"text":"            LogOutput::File(_) => {","highlight_start":1,"highlight_end":31}],"label":null,"suggested_replacement":"LogOutput::StdOut {\n            } |\n            LogOutput::File(_)","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: unexpected `,` in pattern\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mactlogica-logger/src/setting.rs:172:14\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m172\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            },\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: try adding parentheses to match on a tuple...\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m171\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m~ \u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[38;5;10m(\u001b[0m\u001b[0mLogOutput::StdOut {\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m172\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             },\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m173\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m~ \u001b[0m\u001b[0m            LogOutput::File(_)\u001b[0m\u001b[0m\u001b[38;5;10m)\u001b[0m\u001b[0m => {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: ...or a vertical bar to match on multiple alternatives\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m171\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m~ \u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[38;5;10mLogOutput::StdOut {\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m172\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+             } |\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m173\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m~             LogOutput::File(_)\u001b[0m\u001b[0m => {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"binary operation `==` cannot be applied to type `LogOutput`","code":{"code":"E0369","explanation":"A binary operation was attempted on a type which doesn't support it.\n\nErroneous code example:\n\n```compile_fail,E0369\nlet x = 12f32; // error: binary operation `<<` cannot be applied to\n               //        type `f32`\n\nx << 2;\n```\n\nTo fix this error, please check that this type implements this binary\noperation. Example:\n\n```\nlet x = 12u32; // the `u32` type does implement it:\n               // https://doc.rust-lang.org/stable/std/ops/trait.Shl.html\n\nx << 2; // ok!\n```\n\nIt is also possible to overload most operators for your own type by\nimplementing traits from `std::ops`.\n\nString concatenation appends the string on the right to the string on the\nleft and may require reallocation. This requires ownership of the string\non the left. If something should be added to a string literal, move the\nliteral to the heap by allocating it with `to_owned()` like in\n`\"Your text\".to_owned()`.\n"},"level":"error","spans":[{"file_name":"actlogica-logger/src/setting.rs","byte_start":3654,"byte_end":3670,"line_start":109,"line_end":109,"column_start":12,"column_end":28,"is_primary":false,"text":[{"text":"        if self.output_type == LogOutput::StdOut {","highlight_start":12,"highlight_end":28}],"label":"LogOutput","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"actlogica-logger/src/setting.rs","byte_start":3674,"byte_end":3691,"line_start":109,"line_end":109,"column_start":32,"column_end":49,"is_primary":false,"text":[{"text":"        if self.output_type == LogOutput::StdOut {","highlight_start":32,"highlight_end":49}],"label":"LogOutput","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"actlogica-logger/src/setting.rs","byte_start":3671,"byte_end":3673,"line_start":109,"line_end":109,"column_start":29,"column_end":31,"is_primary":true,"text":[{"text":"        if self.output_type == LogOutput::StdOut {","highlight_start":29,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"an implementation of `PartialEq` might be missing for `LogOutput`","code":null,"level":"note","spans":[{"file_name":"actlogica-logger/src/setting.rs","byte_start":578,"byte_end":596,"line_start":20,"line_end":20,"column_start":1,"column_end":19,"is_primary":true,"text":[{"text":"pub enum LogOutput {","highlight_start":1,"highlight_end":19}],"label":"must implement `PartialEq`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"consider annotating `LogOutput` with `#[derive(PartialEq)]`","code":null,"level":"help","spans":[{"file_name":"actlogica-logger/src/setting.rs","byte_start":578,"byte_end":578,"line_start":20,"line_end":20,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"pub enum LogOutput {","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"#[derive(PartialEq)]\n","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0369]\u001b[0m\u001b[0m\u001b[1m: binary operation `==` cannot be applied to type `LogOutput`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mactlogica-logger/src/setting.rs:109:29\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m109\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        if self.output_type == LogOutput::StdOut {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mLogOutput\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mLogOutput\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: an implementation of `PartialEq` might be missing for `LogOutput`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mactlogica-logger/src/setting.rs:20:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m20\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub enum LogOutput {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mmust implement `PartialEq`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: consider annotating `LogOutput` with `#[derive(PartialEq)]`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m20\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[38;5;10m+ #[derive(PartialEq)]\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m21\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m| \u001b[0m\u001b[0mpub enum LogOutput {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"aborting due to 2 previous errors","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: aborting due to 2 previous errors\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"For more information about this error, try `rustc --explain E0369`.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1mFor more information about this error, try `rustc --explain E0369`.\u001b[0m\n"}
