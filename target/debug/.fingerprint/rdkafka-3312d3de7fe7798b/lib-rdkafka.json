{"rustc": 5148484765029645790, "features": "[\"default\", \"libz\", \"tokio\"]", "declared_features": "[\"cmake-build\", \"cmake_build\", \"curl\", \"curl-static\", \"default\", \"dynamic-linking\", \"dynamic_linking\", \"external-lz4\", \"external_lz4\", \"futures-executor\", \"gssapi\", \"gssapi-vendored\", \"libz\", \"libz-static\", \"naive-runtime\", \"sasl\", \"ssl\", \"ssl-vendored\", \"tokio\", \"tracing\", \"zstd\", \"zstd-pkg-config\"]", "target": 10655332816260798685, "profile": 8276155916380437441, "path": 2330436358086211675, "deps": [[1811549171721445101, "futures_channel", false, 1461379806140217563], [2924422107542798392, "libc", false, 4984182320319247072], [4560922040912186210, "rdkafka_sys", false, 15917214923637596285], [5138218615291878843, "tokio", false, 9561769989935279160], [5986029879202738730, "log", false, 15378477431589452845], [6955678925937229351, "slab", false, 7770454811681072785], [9689903380558560274, "serde", false, 8904198866590005089], [10629569228670356391, "futures_util", false, 6148317299109235802], [15367738274754116744, "serde_json", false, 9740411289623250564], [16257276029081467297, "serde_derive", false, 8210338533385978999]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/rdkafka-3312d3de7fe7798b/dep-lib-rdkafka", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}