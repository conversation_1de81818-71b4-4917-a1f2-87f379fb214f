{"rustc": 5148484765029645790, "features": "[\"libz\", \"libz-sys\", \"openssl-sys\", \"ssl\"]", "declared_features": "[\"cmake\", \"cmake-build\", \"cmake_build\", \"curl\", \"curl-static\", \"curl-sys\", \"default\", \"dynamic-linking\", \"dynamic_linking\", \"external-lz4\", \"external_lz4\", \"gssapi\", \"gssapi-vendored\", \"libz\", \"libz-static\", \"libz-sys\", \"lz4-sys\", \"openssl-sys\", \"sasl\", \"sasl2-sys\", \"ssl\", \"ssl-vendored\", \"zstd\", \"zstd-pkg-config\", \"zstd-sys\"]", "target": 17828483436349078361, "profile": 5347358027863023418, "path": 9338172746266259814, "deps": [[1470679118034951355, "num_enum", false, 3523746357809567954], [2924422107542798392, "libc", false, 17477234760199531588], [4560922040912186210, "build_script_build", false, 2517811328903665785], [9070360545695802481, "openssl_sys", false, 9411705820582551528], [17022423707615322322, "libz_sys", false, 14617776722094532008]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/rdkafka-sys-c4860356f1017575/dep-lib-rdkafka_sys", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}