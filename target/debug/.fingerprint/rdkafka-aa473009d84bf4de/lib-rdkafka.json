{"rustc": 5148484765029645790, "features": "[\"default\", \"libz\", \"tokio\"]", "declared_features": "[\"cmake-build\", \"cmake_build\", \"curl\", \"curl-static\", \"default\", \"dynamic-linking\", \"dynamic_linking\", \"external-lz4\", \"external_lz4\", \"futures-executor\", \"gssapi\", \"gssapi-vendored\", \"libz\", \"libz-static\", \"naive-runtime\", \"sasl\", \"ssl\", \"ssl-vendored\", \"tokio\", \"tracing\", \"zstd\", \"zstd-pkg-config\"]", "target": 10655332816260798685, "profile": 5347358027863023418, "path": 2330436358086211675, "deps": [[1811549171721445101, "futures_channel", false, 6784863043605917854], [2924422107542798392, "libc", false, 17477234760199531588], [4560922040912186210, "rdkafka_sys", false, 503011016196003486], [5138218615291878843, "tokio", false, 16095950932233218745], [5986029879202738730, "log", false, 10055385268587598666], [6955678925937229351, "slab", false, 15378497086024815131], [9689903380558560274, "serde", false, 1133203805763328567], [10629569228670356391, "futures_util", false, 16849944574023364058], [15367738274754116744, "serde_json", false, 7223384620422996608], [16257276029081467297, "serde_derive", false, 8210338533385978999]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/rdkafka-aa473009d84bf4de/dep-lib-rdkafka", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}