{"rustc": 5148484765029645790, "features": "[]", "declared_features": "[]", "target": 14840160241370058439, "profile": 2330448797067240312, "path": 18017183740903701435, "deps": [[1760623714118191065, "dotenv", false, 2666915110836315068], [5138218615291878843, "tokio", false, 9561769989935279160], [6547980334806251551, "chrono", false, 14492348561620866927], [6601542651617637986, "base62", false, 1165239560603879161], [8269115081296425610, "uuid", false, 16065861552154467195], [8606274917505247608, "tracing", false, 6857282640855977722], [9689903380558560274, "serde", false, 8904198866590005089], [11033263105862272874, "tracing_core", false, 5810636522752067551], [11594979262886006466, "tracing_appender", false, 17731245061710428328], [12371715326520714823, "rdkafka", false, 3597976855571754931], [15367738274754116744, "serde_json", false, 12269274112852569023], [16230660778393187092, "tracing_subscriber", false, 12622776075504040724]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/actlogica_logs-05671dcb5cb81beb/dep-lib-actlogica_logs", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}