{"$message_type":"diagnostic","message":"the placeholder `_` is not allowed within types on item signatures for return types","code":{"code":"E0121","explanation":"The type placeholder `_` was used within a type on an item's signature.\n\nErroneous code example:\n\n```compile_fail,E0121\nfn foo() -> _ { 5 } // error\n\nstatic BAR: _ = \"test\"; // error\n```\n\nIn those cases, you need to provide the type explicitly:\n\n```\nfn foo() -> i32 { 5 } // ok!\n\nstatic BAR: &str = \"test\"; // ok!\n```\n\nThe type placeholder `_` can be used outside item's signature as follows:\n\n```\nlet x = \"a4a\".split('4')\n    .collect::<Vec<_>>(); // No need to precise the Vec's generic type.\n```\n"},"level":"error","spans":[{"file_name":"actlogica-logger/src/utils.rs","byte_start":1718,"byte_end":1719,"line_start":57,"line_end":57,"column_start":86,"column_end":87,"is_primary":true,"text":[{"text":"pub fn human_layer() -> tracing_subscriber::fmt::Layer<tracing_subscriber::Registry, _> {","highlight_start":86,"highlight_end":87}],"label":"not allowed in type signatures","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"replace with the correct return type","code":null,"level":"help","spans":[{"file_name":"actlogica-logger/src/utils.rs","byte_start":1657,"byte_end":1720,"line_start":57,"line_end":57,"column_start":25,"column_end":88,"is_primary":true,"text":[{"text":"pub fn human_layer() -> tracing_subscriber::fmt::Layer<tracing_subscriber::Registry, _> {","highlight_start":25,"highlight_end":88}],"label":null,"suggested_replacement":"tracing_subscriber::fmt::Layer<Registry>","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0121]\u001b[0m\u001b[0m\u001b[1m: the placeholder `_` is not allowed within types on item signatures for return types\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mactlogica-logger/src/utils.rs:57:86\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m57\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub fn human_layer() -> tracing_subscriber::fmt::Layer<tracing_subscriber::Registry, _> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-------------------------------------------------------------\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mnot allowed in type signatures\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mhelp: replace with the correct return type: `tracing_subscriber::fmt::Layer<Registry>`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the trait bound `Layer<Registry>: Layer<Layered<LevelFilter, Registry>>` is not satisfied","code":{"code":"E0277","explanation":"You tried to use a type which doesn't implement some trait in a place which\nexpected that trait.\n\nErroneous code example:\n\n```compile_fail,E0277\n// here we declare the Foo trait with a bar method\ntrait Foo {\n    fn bar(&self);\n}\n\n// we now declare a function which takes an object implementing the Foo trait\nfn some_func<T: Foo>(foo: T) {\n    foo.bar();\n}\n\nfn main() {\n    // we now call the method with the i32 type, which doesn't implement\n    // the Foo trait\n    some_func(5i32); // error: the trait bound `i32 : Foo` is not satisfied\n}\n```\n\nIn order to fix this error, verify that the type you're using does implement\nthe trait. Example:\n\n```\ntrait Foo {\n    fn bar(&self);\n}\n\n// we implement the trait on the i32 type\nimpl Foo for i32 {\n    fn bar(&self) {}\n}\n\nfn some_func<T: Foo>(foo: T) {\n    foo.bar(); // we can now use this method since i32 implements the\n               // Foo trait\n}\n\nfn main() {\n    some_func(5i32); // ok!\n}\n```\n\nOr in a generic context, an erroneous code example would look like:\n\n```compile_fail,E0277\nfn some_func<T>(foo: T) {\n    println!(\"{:?}\", foo); // error: the trait `core::fmt::Debug` is not\n                           //        implemented for the type `T`\n}\n\nfn main() {\n    // We now call the method with the i32 type,\n    // which *does* implement the Debug trait.\n    some_func(5i32);\n}\n```\n\nNote that the error here is in the definition of the generic function. Although\nwe only call it with a parameter that does implement `Debug`, the compiler\nstill rejects the function. It must work with all possible input types. In\norder to make this example compile, we need to restrict the generic type we're\naccepting:\n\n```\nuse std::fmt;\n\n// Restrict the input type to types that implement Debug.\nfn some_func<T: fmt::Debug>(foo: T) {\n    println!(\"{:?}\", foo);\n}\n\nfn main() {\n    // Calling the method is still fine, as i32 implements Debug.\n    some_func(5i32);\n\n    // This would fail to compile now:\n    // struct WithoutDebug;\n    // some_func(WithoutDebug);\n}\n```\n\nRust only looks at the signature of the called function, as such it must\nalready specify all requirements that will be used for every type parameter.\n"},"level":"error","spans":[{"file_name":"actlogica-logger/src/setting.rs","byte_start":1183,"byte_end":1196,"line_start":40,"line_end":40,"column_start":69,"column_end":82,"is_primary":true,"text":[{"text":"            tracing::subscriber::set_global_default(subscriber.with(human_layer()))","highlight_start":69,"highlight_end":82}],"label":"the trait `__tracing_subscriber_Layer<Layered<LevelFilter, Registry>>` is not implemented for `tracing_subscriber::fmt::Layer<Registry>`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"actlogica-logger/src/setting.rs","byte_start":1178,"byte_end":1182,"line_start":40,"line_end":40,"column_start":64,"column_end":68,"is_primary":false,"text":[{"text":"            tracing::subscriber::set_global_default(subscriber.with(human_layer()))","highlight_start":64,"highlight_end":68}],"label":"required by a bound introduced by this call","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"the trait `Layer<Layered<LevelFilter, Registry>>` is not implemented for `tracing_subscriber::fmt::Layer<Registry>`\nbut trait `Layer<Registry>` is implemented for it","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"for that trait implementation, expected `Registry`, found `Layered<LevelFilter, Registry>`","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"required by a bound in `with`","code":null,"level":"note","spans":[{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tracing-subscriber-0.3.19/src/layer/mod.rs","byte_start":62379,"byte_end":62383,"line_start":1502,"line_end":1502,"column_start":8,"column_end":12,"is_primary":false,"text":[{"text":"    fn with<L>(self, layer: L) -> Layered<L, Self>","highlight_start":8,"highlight_end":12}],"label":"required by a bound in this associated function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tracing-subscriber-0.3.19/src/layer/mod.rs","byte_start":62444,"byte_end":62455,"line_start":1504,"line_end":1504,"column_start":12,"column_end":23,"is_primary":true,"text":[{"text":"        L: Layer<Self>,","highlight_start":12,"highlight_end":23}],"label":"required by this bound in `__tracing_subscriber_SubscriberExt::with`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"the full name for the type has been written to '/Users/<USER>/Desktop/logger/target/debug/deps/actlogica_logs-7311386296642d7a.long-type-2689522479973415783.txt'","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"consider using `--verbose` to print the full type name to the console","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0277]\u001b[0m\u001b[0m\u001b[1m: the trait bound `Layer<Registry>: Layer<Layered<LevelFilter, Registry>>` is not satisfied\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mactlogica-logger/src/setting.rs:40:69\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m40\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            tracing::subscriber::set_global_default(subscriber.with(human_layer()))\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mthe trait `__tracing_subscriber_Layer<Layered<LevelFilter, Registry>>` is not implemented for `tracing_subscriber::fmt::Layer<Registry>`\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mrequired by a bound introduced by this call\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: the trait `Layer<\u001b[0m\u001b[0m\u001b[1m\u001b[35mLayered<LevelFilter\u001b[0m\u001b[0m, Registry\u001b[0m\u001b[0m\u001b[1m\u001b[35m>\u001b[0m\u001b[0m>` \u001b[0m\u001b[0m\u001b[1m\u001b[35mis not\u001b[0m\u001b[0m implemented for `tracing_subscriber::fmt::Layer<Registry>`\u001b[0m\n\u001b[0m             but trait `Layer<Registry>` \u001b[0m\u001b[0m\u001b[1m\u001b[35mis\u001b[0m\u001b[0m implemented for it\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for that trait implementation, expected `\u001b[0m\u001b[0m\u001b[1m\u001b[35mRegistry\u001b[0m\u001b[0m`, found `\u001b[0m\u001b[0m\u001b[1m\u001b[35mLayered<LevelFilter, Registry>\u001b[0m\u001b[0m`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: required by a bound in `with`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tracing-subscriber-0.3.19/src/layer/mod.rs:1504:12\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1502\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn with<L>(self, layer: L) -> Layered<L, Self>\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mrequired by a bound in this associated function\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1503\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    where\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1504\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        L: Layer<Self>,\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mrequired by this bound in `__tracing_subscriber_SubscriberExt::with`\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: the full name for the type has been written to '/Users/<USER>/Desktop/logger/target/debug/deps/actlogica_logs-7311386296642d7a.long-type-2689522479973415783.txt'\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: consider using `--verbose` to print the full type name to the console\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the trait bound `Layer<Registry>: Layer<Layered<LevelFilter, Registry>>` is not satisfied","code":{"code":"E0277","explanation":"You tried to use a type which doesn't implement some trait in a place which\nexpected that trait.\n\nErroneous code example:\n\n```compile_fail,E0277\n// here we declare the Foo trait with a bar method\ntrait Foo {\n    fn bar(&self);\n}\n\n// we now declare a function which takes an object implementing the Foo trait\nfn some_func<T: Foo>(foo: T) {\n    foo.bar();\n}\n\nfn main() {\n    // we now call the method with the i32 type, which doesn't implement\n    // the Foo trait\n    some_func(5i32); // error: the trait bound `i32 : Foo` is not satisfied\n}\n```\n\nIn order to fix this error, verify that the type you're using does implement\nthe trait. Example:\n\n```\ntrait Foo {\n    fn bar(&self);\n}\n\n// we implement the trait on the i32 type\nimpl Foo for i32 {\n    fn bar(&self) {}\n}\n\nfn some_func<T: Foo>(foo: T) {\n    foo.bar(); // we can now use this method since i32 implements the\n               // Foo trait\n}\n\nfn main() {\n    some_func(5i32); // ok!\n}\n```\n\nOr in a generic context, an erroneous code example would look like:\n\n```compile_fail,E0277\nfn some_func<T>(foo: T) {\n    println!(\"{:?}\", foo); // error: the trait `core::fmt::Debug` is not\n                           //        implemented for the type `T`\n}\n\nfn main() {\n    // We now call the method with the i32 type,\n    // which *does* implement the Debug trait.\n    some_func(5i32);\n}\n```\n\nNote that the error here is in the definition of the generic function. Although\nwe only call it with a parameter that does implement `Debug`, the compiler\nstill rejects the function. It must work with all possible input types. In\norder to make this example compile, we need to restrict the generic type we're\naccepting:\n\n```\nuse std::fmt;\n\n// Restrict the input type to types that implement Debug.\nfn some_func<T: fmt::Debug>(foo: T) {\n    println!(\"{:?}\", foo);\n}\n\nfn main() {\n    // Calling the method is still fine, as i32 implements Debug.\n    some_func(5i32);\n\n    // This would fail to compile now:\n    // struct WithoutDebug;\n    // some_func(WithoutDebug);\n}\n```\n\nRust only looks at the signature of the called function, as such it must\nalready specify all requirements that will be used for every type parameter.\n"},"level":"error","spans":[{"file_name":"actlogica-logger/src/setting.rs","byte_start":1167,"byte_end":1197,"line_start":40,"line_end":40,"column_start":53,"column_end":83,"is_primary":true,"text":[{"text":"            tracing::subscriber::set_global_default(subscriber.with(human_layer()))","highlight_start":53,"highlight_end":83}],"label":"the trait `__tracing_subscriber_Layer<Layered<LevelFilter, Registry>>` is not implemented for `tracing_subscriber::fmt::Layer<Registry>`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"actlogica-logger/src/setting.rs","byte_start":1127,"byte_end":1166,"line_start":40,"line_end":40,"column_start":13,"column_end":52,"is_primary":false,"text":[{"text":"            tracing::subscriber::set_global_default(subscriber.with(human_layer()))","highlight_start":13,"highlight_end":52}],"label":"required by a bound introduced by this call","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"the trait `Layer<Layered<LevelFilter, Registry>>` is not implemented for `tracing_subscriber::fmt::Layer<Registry>`\nbut trait `Layer<Registry>` is implemented for it","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"for that trait implementation, expected `Registry`, found `Layered<LevelFilter, Registry>`","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"required for `Layered<tracing_subscriber::fmt::Layer<Registry>, Layered<LevelFilter, Registry>>` to implement `tracing::Subscriber`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"required by a bound in `tracing::subscriber::set_global_default`","code":null,"level":"note","spans":[{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tracing-0.1.41/src/subscriber.rs","byte_start":1371,"byte_end":1389,"line_start":39,"line_end":39,"column_start":8,"column_end":26,"is_primary":false,"text":[{"text":"pub fn set_global_default<S>(subscriber: S) -> Result<(), SetGlobalDefaultError>","highlight_start":8,"highlight_end":26}],"label":"required by a bound in this function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tracing-0.1.41/src/subscriber.rs","byte_start":1458,"byte_end":1468,"line_start":41,"line_end":41,"column_start":8,"column_end":18,"is_primary":true,"text":[{"text":"    S: Subscriber + Send + Sync + 'static,","highlight_start":8,"highlight_end":18}],"label":"required by this bound in `set_global_default`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"the full name for the type has been written to '/Users/<USER>/Desktop/logger/target/debug/deps/actlogica_logs-7311386296642d7a.long-type-2689522479973415783.txt'","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"consider using `--verbose` to print the full type name to the console","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0277]\u001b[0m\u001b[0m\u001b[1m: the trait bound `Layer<Registry>: Layer<Layered<LevelFilter, Registry>>` is not satisfied\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mactlogica-logger/src/setting.rs:40:53\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m40\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            tracing::subscriber::set_global_default(subscriber.with(human_layer()))\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m---------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mthe trait `__tracing_subscriber_Layer<Layered<LevelFilter, Registry>>` is not implemented for `tracing_subscriber::fmt::Layer<Registry>`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mrequired by a bound introduced by this call\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: the trait `Layer<\u001b[0m\u001b[0m\u001b[1m\u001b[35mLayered<LevelFilter\u001b[0m\u001b[0m, Registry\u001b[0m\u001b[0m\u001b[1m\u001b[35m>\u001b[0m\u001b[0m>` \u001b[0m\u001b[0m\u001b[1m\u001b[35mis not\u001b[0m\u001b[0m implemented for `tracing_subscriber::fmt::Layer<Registry>`\u001b[0m\n\u001b[0m           but trait `Layer<Registry>` \u001b[0m\u001b[0m\u001b[1m\u001b[35mis\u001b[0m\u001b[0m implemented for it\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for that trait implementation, expected `\u001b[0m\u001b[0m\u001b[1m\u001b[35mRegistry\u001b[0m\u001b[0m`, found `\u001b[0m\u001b[0m\u001b[1m\u001b[35mLayered<LevelFilter, Registry>\u001b[0m\u001b[0m`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: required for `Layered<tracing_subscriber::fmt::Layer<Registry>, Layered<LevelFilter, Registry>>` to implement `tracing::Subscriber`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: required by a bound in `tracing::subscriber::set_global_default`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tracing-0.1.41/src/subscriber.rs:41:8\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m39\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub fn set_global_default<S>(subscriber: S) -> Result<(), SetGlobalDefaultError>\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mrequired by a bound in this function\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m40\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mwhere\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m41\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    S: Subscriber + Send + Sync + 'static,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mrequired by this bound in `set_global_default`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: the full name for the type has been written to '/Users/<USER>/Desktop/logger/target/debug/deps/actlogica_logs-7311386296642d7a.long-type-2689522479973415783.txt'\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: consider using `--verbose` to print the full type name to the console\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the trait bound `Layer<Registry, CustomFormatter>: Layer<Layered<LevelFilter, ...>>` is not satisfied","code":{"code":"E0277","explanation":"You tried to use a type which doesn't implement some trait in a place which\nexpected that trait.\n\nErroneous code example:\n\n```compile_fail,E0277\n// here we declare the Foo trait with a bar method\ntrait Foo {\n    fn bar(&self);\n}\n\n// we now declare a function which takes an object implementing the Foo trait\nfn some_func<T: Foo>(foo: T) {\n    foo.bar();\n}\n\nfn main() {\n    // we now call the method with the i32 type, which doesn't implement\n    // the Foo trait\n    some_func(5i32); // error: the trait bound `i32 : Foo` is not satisfied\n}\n```\n\nIn order to fix this error, verify that the type you're using does implement\nthe trait. Example:\n\n```\ntrait Foo {\n    fn bar(&self);\n}\n\n// we implement the trait on the i32 type\nimpl Foo for i32 {\n    fn bar(&self) {}\n}\n\nfn some_func<T: Foo>(foo: T) {\n    foo.bar(); // we can now use this method since i32 implements the\n               // Foo trait\n}\n\nfn main() {\n    some_func(5i32); // ok!\n}\n```\n\nOr in a generic context, an erroneous code example would look like:\n\n```compile_fail,E0277\nfn some_func<T>(foo: T) {\n    println!(\"{:?}\", foo); // error: the trait `core::fmt::Debug` is not\n                           //        implemented for the type `T`\n}\n\nfn main() {\n    // We now call the method with the i32 type,\n    // which *does* implement the Debug trait.\n    some_func(5i32);\n}\n```\n\nNote that the error here is in the definition of the generic function. Although\nwe only call it with a parameter that does implement `Debug`, the compiler\nstill rejects the function. It must work with all possible input types. In\norder to make this example compile, we need to restrict the generic type we're\naccepting:\n\n```\nuse std::fmt;\n\n// Restrict the input type to types that implement Debug.\nfn some_func<T: fmt::Debug>(foo: T) {\n    println!(\"{:?}\", foo);\n}\n\nfn main() {\n    // Calling the method is still fine, as i32 implements Debug.\n    some_func(5i32);\n\n    // This would fail to compile now:\n    // struct WithoutDebug;\n    // some_func(WithoutDebug);\n}\n```\n\nRust only looks at the signature of the called function, as such it must\nalready specify all requirements that will be used for every type parameter.\n"},"level":"error","spans":[{"file_name":"actlogica-logger/src/setting.rs","byte_start":1782,"byte_end":1827,"line_start":53,"line_end":53,"column_start":33,"column_end":78,"is_primary":true,"text":[{"text":"                subscriber.with(json_layer(service_name, output_type.clone())),","highlight_start":33,"highlight_end":78}],"label":"unsatisfied trait bound","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"actlogica-logger/src/setting.rs","byte_start":1777,"byte_end":1781,"line_start":53,"line_end":53,"column_start":28,"column_end":32,"is_primary":false,"text":[{"text":"                subscriber.with(json_layer(service_name, output_type.clone())),","highlight_start":28,"highlight_end":32}],"label":"required by a bound introduced by this call","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"the trait `__tracing_subscriber_Layer<Layered<LevelFilter, Registry>>` is not implemented for `tracing_subscriber::fmt::Layer<Registry, CustomFormatter>`","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"the trait `__tracing_subscriber_Layer<S>` is implemented for `tracing_subscriber::fmt::Layer<S, N, E, W>`","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"required by a bound in `with`","code":null,"level":"note","spans":[{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tracing-subscriber-0.3.19/src/layer/mod.rs","byte_start":62379,"byte_end":62383,"line_start":1502,"line_end":1502,"column_start":8,"column_end":12,"is_primary":false,"text":[{"text":"    fn with<L>(self, layer: L) -> Layered<L, Self>","highlight_start":8,"highlight_end":12}],"label":"required by a bound in this associated function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tracing-subscriber-0.3.19/src/layer/mod.rs","byte_start":62444,"byte_end":62455,"line_start":1504,"line_end":1504,"column_start":12,"column_end":23,"is_primary":true,"text":[{"text":"        L: Layer<Self>,","highlight_start":12,"highlight_end":23}],"label":"required by this bound in `__tracing_subscriber_SubscriberExt::with`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"the full name for the type has been written to '/Users/<USER>/Desktop/logger/target/debug/deps/actlogica_logs-7311386296642d7a.long-type-9545663298250586398.txt'","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"consider using `--verbose` to print the full type name to the console","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0277]\u001b[0m\u001b[0m\u001b[1m: the trait bound `Layer<Registry, CustomFormatter>: Layer<Layered<LevelFilter, ...>>` is not satisfied\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mactlogica-logger/src/setting.rs:53:33\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m53\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                subscriber.with(json_layer(service_name, output_type.clone())),\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9munsatisfied trait bound\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mrequired by a bound introduced by this call\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: the trait `__tracing_subscriber_Layer<Layered<LevelFilter, Registry>>` is not implemented for `tracing_subscriber::fmt::Layer<Registry, CustomFormatter>`\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: the trait `__tracing_subscriber_Layer<S>` \u001b[0m\u001b[0m\u001b[1m\u001b[35mis\u001b[0m\u001b[0m implemented for `\u001b[0m\u001b[0m\u001b[1m\u001b[35mtracing_subscriber::fmt::Layer<S, N, E, W>\u001b[0m\u001b[0m`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: required by a bound in `with`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tracing-subscriber-0.3.19/src/layer/mod.rs:1504:12\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1502\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn with<L>(self, layer: L) -> Layered<L, Self>\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mrequired by a bound in this associated function\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1503\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    where\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1504\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        L: Layer<Self>,\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mrequired by this bound in `__tracing_subscriber_SubscriberExt::with`\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: the full name for the type has been written to '/Users/<USER>/Desktop/logger/target/debug/deps/actlogica_logs-7311386296642d7a.long-type-9545663298250586398.txt'\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: consider using `--verbose` to print the full type name to the console\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the trait bound `Layer<Registry, CustomFormatter>: Layer<Layered<LevelFilter, ...>>` is not satisfied","code":{"code":"E0277","explanation":"You tried to use a type which doesn't implement some trait in a place which\nexpected that trait.\n\nErroneous code example:\n\n```compile_fail,E0277\n// here we declare the Foo trait with a bar method\ntrait Foo {\n    fn bar(&self);\n}\n\n// we now declare a function which takes an object implementing the Foo trait\nfn some_func<T: Foo>(foo: T) {\n    foo.bar();\n}\n\nfn main() {\n    // we now call the method with the i32 type, which doesn't implement\n    // the Foo trait\n    some_func(5i32); // error: the trait bound `i32 : Foo` is not satisfied\n}\n```\n\nIn order to fix this error, verify that the type you're using does implement\nthe trait. Example:\n\n```\ntrait Foo {\n    fn bar(&self);\n}\n\n// we implement the trait on the i32 type\nimpl Foo for i32 {\n    fn bar(&self) {}\n}\n\nfn some_func<T: Foo>(foo: T) {\n    foo.bar(); // we can now use this method since i32 implements the\n               // Foo trait\n}\n\nfn main() {\n    some_func(5i32); // ok!\n}\n```\n\nOr in a generic context, an erroneous code example would look like:\n\n```compile_fail,E0277\nfn some_func<T>(foo: T) {\n    println!(\"{:?}\", foo); // error: the trait `core::fmt::Debug` is not\n                           //        implemented for the type `T`\n}\n\nfn main() {\n    // We now call the method with the i32 type,\n    // which *does* implement the Debug trait.\n    some_func(5i32);\n}\n```\n\nNote that the error here is in the definition of the generic function. Although\nwe only call it with a parameter that does implement `Debug`, the compiler\nstill rejects the function. It must work with all possible input types. In\norder to make this example compile, we need to restrict the generic type we're\naccepting:\n\n```\nuse std::fmt;\n\n// Restrict the input type to types that implement Debug.\nfn some_func<T: fmt::Debug>(foo: T) {\n    println!(\"{:?}\", foo);\n}\n\nfn main() {\n    // Calling the method is still fine, as i32 implements Debug.\n    some_func(5i32);\n\n    // This would fail to compile now:\n    // struct WithoutDebug;\n    // some_func(WithoutDebug);\n}\n```\n\nRust only looks at the signature of the called function, as such it must\nalready specify all requirements that will be used for every type parameter.\n"},"level":"error","spans":[{"file_name":"actlogica-logger/src/setting.rs","byte_start":1766,"byte_end":1828,"line_start":53,"line_end":53,"column_start":17,"column_end":79,"is_primary":true,"text":[{"text":"                subscriber.with(json_layer(service_name, output_type.clone())),","highlight_start":17,"highlight_end":79}],"label":"unsatisfied trait bound","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"actlogica-logger/src/setting.rs","byte_start":1709,"byte_end":1748,"line_start":52,"line_end":52,"column_start":13,"column_end":52,"is_primary":false,"text":[{"text":"            tracing::subscriber::set_global_default(","highlight_start":13,"highlight_end":52}],"label":"required by a bound introduced by this call","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"the trait `__tracing_subscriber_Layer<Layered<LevelFilter, Registry>>` is not implemented for `tracing_subscriber::fmt::Layer<Registry, CustomFormatter>`","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"the trait `__tracing_subscriber_Layer<S>` is implemented for `tracing_subscriber::fmt::Layer<S, N, E, W>`","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"required for `Layered<Layer<Registry, CustomFormatter>, Layered<LevelFilter, ...>>` to implement `tracing::Subscriber`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"required by a bound in `tracing::subscriber::set_global_default`","code":null,"level":"note","spans":[{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tracing-0.1.41/src/subscriber.rs","byte_start":1371,"byte_end":1389,"line_start":39,"line_end":39,"column_start":8,"column_end":26,"is_primary":false,"text":[{"text":"pub fn set_global_default<S>(subscriber: S) -> Result<(), SetGlobalDefaultError>","highlight_start":8,"highlight_end":26}],"label":"required by a bound in this function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tracing-0.1.41/src/subscriber.rs","byte_start":1458,"byte_end":1468,"line_start":41,"line_end":41,"column_start":8,"column_end":18,"is_primary":true,"text":[{"text":"    S: Subscriber + Send + Sync + 'static,","highlight_start":8,"highlight_end":18}],"label":"required by this bound in `set_global_default`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"the full name for the type has been written to '/Users/<USER>/Desktop/logger/target/debug/deps/actlogica_logs-7311386296642d7a.long-type-9545663298250586398.txt'","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"consider using `--verbose` to print the full type name to the console","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0277]\u001b[0m\u001b[0m\u001b[1m: the trait bound `Layer<Registry, CustomFormatter>: Layer<Layered<LevelFilter, ...>>` is not satisfied\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mactlogica-logger/src/setting.rs:53:17\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m52\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            tracing::subscriber::set_global_default(\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m---------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mrequired by a bound introduced by this call\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m53\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                subscriber.with(json_layer(service_name, output_type.clone())),\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9munsatisfied trait bound\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: the trait `__tracing_subscriber_Layer<Layered<LevelFilter, Registry>>` is not implemented for `tracing_subscriber::fmt::Layer<Registry, CustomFormatter>`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: the trait `__tracing_subscriber_Layer<S>` \u001b[0m\u001b[0m\u001b[1m\u001b[35mis\u001b[0m\u001b[0m implemented for `\u001b[0m\u001b[0m\u001b[1m\u001b[35mtracing_subscriber::fmt::Layer<S, N, E, W>\u001b[0m\u001b[0m`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: required for `Layered<Layer<Registry, CustomFormatter>, Layered<LevelFilter, ...>>` to implement `tracing::Subscriber`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: required by a bound in `tracing::subscriber::set_global_default`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tracing-0.1.41/src/subscriber.rs:41:8\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m39\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub fn set_global_default<S>(subscriber: S) -> Result<(), SetGlobalDefaultError>\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mrequired by a bound in this function\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m40\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mwhere\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m41\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    S: Subscriber + Send + Sync + 'static,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mrequired by this bound in `set_global_default`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: the full name for the type has been written to '/Users/<USER>/Desktop/logger/target/debug/deps/actlogica_logs-7311386296642d7a.long-type-9545663298250586398.txt'\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: consider using `--verbose` to print the full type name to the console\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the trait bound `Layer<Registry, CustomFormatter, Format, NonBlocking>: Layer<...>` is not satisfied","code":{"code":"E0277","explanation":"You tried to use a type which doesn't implement some trait in a place which\nexpected that trait.\n\nErroneous code example:\n\n```compile_fail,E0277\n// here we declare the Foo trait with a bar method\ntrait Foo {\n    fn bar(&self);\n}\n\n// we now declare a function which takes an object implementing the Foo trait\nfn some_func<T: Foo>(foo: T) {\n    foo.bar();\n}\n\nfn main() {\n    // we now call the method with the i32 type, which doesn't implement\n    // the Foo trait\n    some_func(5i32); // error: the trait bound `i32 : Foo` is not satisfied\n}\n```\n\nIn order to fix this error, verify that the type you're using does implement\nthe trait. Example:\n\n```\ntrait Foo {\n    fn bar(&self);\n}\n\n// we implement the trait on the i32 type\nimpl Foo for i32 {\n    fn bar(&self) {}\n}\n\nfn some_func<T: Foo>(foo: T) {\n    foo.bar(); // we can now use this method since i32 implements the\n               // Foo trait\n}\n\nfn main() {\n    some_func(5i32); // ok!\n}\n```\n\nOr in a generic context, an erroneous code example would look like:\n\n```compile_fail,E0277\nfn some_func<T>(foo: T) {\n    println!(\"{:?}\", foo); // error: the trait `core::fmt::Debug` is not\n                           //        implemented for the type `T`\n}\n\nfn main() {\n    // We now call the method with the i32 type,\n    // which *does* implement the Debug trait.\n    some_func(5i32);\n}\n```\n\nNote that the error here is in the definition of the generic function. Although\nwe only call it with a parameter that does implement `Debug`, the compiler\nstill rejects the function. It must work with all possible input types. In\norder to make this example compile, we need to restrict the generic type we're\naccepting:\n\n```\nuse std::fmt;\n\n// Restrict the input type to types that implement Debug.\nfn some_func<T: fmt::Debug>(foo: T) {\n    println!(\"{:?}\", foo);\n}\n\nfn main() {\n    // Calling the method is still fine, as i32 implements Debug.\n    some_func(5i32);\n\n    // This would fail to compile now:\n    // struct WithoutDebug;\n    // some_func(WithoutDebug);\n}\n```\n\nRust only looks at the signature of the called function, as such it must\nalready specify all requirements that will be used for every type parameter.\n"},"level":"error","spans":[{"file_name":"actlogica-logger/src/setting.rs","byte_start":2250,"byte_end":2259,"line_start":61,"line_end":61,"column_start":69,"column_end":78,"is_primary":true,"text":[{"text":"            tracing::subscriber::set_global_default(subscriber.with(fmt_layer))","highlight_start":69,"highlight_end":78}],"label":"unsatisfied trait bound","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"actlogica-logger/src/setting.rs","byte_start":2245,"byte_end":2249,"line_start":61,"line_end":61,"column_start":64,"column_end":68,"is_primary":false,"text":[{"text":"            tracing::subscriber::set_global_default(subscriber.with(fmt_layer))","highlight_start":64,"highlight_end":68}],"label":"required by a bound introduced by this call","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"the trait `__tracing_subscriber_Layer<Layered<LevelFilter, Registry>>` is not implemented for `tracing_subscriber::fmt::Layer<Registry, CustomFormatter, Format, NonBlocking>`","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"the trait `__tracing_subscriber_Layer<S>` is implemented for `tracing_subscriber::fmt::Layer<S, N, E, W>`","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"required by a bound in `with`","code":null,"level":"note","spans":[{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tracing-subscriber-0.3.19/src/layer/mod.rs","byte_start":62379,"byte_end":62383,"line_start":1502,"line_end":1502,"column_start":8,"column_end":12,"is_primary":false,"text":[{"text":"    fn with<L>(self, layer: L) -> Layered<L, Self>","highlight_start":8,"highlight_end":12}],"label":"required by a bound in this associated function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tracing-subscriber-0.3.19/src/layer/mod.rs","byte_start":62444,"byte_end":62455,"line_start":1504,"line_end":1504,"column_start":12,"column_end":23,"is_primary":true,"text":[{"text":"        L: Layer<Self>,","highlight_start":12,"highlight_end":23}],"label":"required by this bound in `__tracing_subscriber_SubscriberExt::with`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"the full name for the type has been written to '/Users/<USER>/Desktop/logger/target/debug/deps/actlogica_logs-7311386296642d7a.long-type-2831091847738482321.txt'","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"consider using `--verbose` to print the full type name to the console","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0277]\u001b[0m\u001b[0m\u001b[1m: the trait bound `Layer<Registry, CustomFormatter, Format, NonBlocking>: Layer<...>` is not satisfied\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mactlogica-logger/src/setting.rs:61:69\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m61\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            tracing::subscriber::set_global_default(subscriber.with(fmt_layer))\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9munsatisfied trait bound\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mrequired by a bound introduced by this call\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: the trait `__tracing_subscriber_Layer<Layered<LevelFilter, Registry>>` is not implemented for `tracing_subscriber::fmt::Layer<Registry, CustomFormatter, Format, NonBlocking>`\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: the trait `__tracing_subscriber_Layer<S>` \u001b[0m\u001b[0m\u001b[1m\u001b[35mis\u001b[0m\u001b[0m implemented for `\u001b[0m\u001b[0m\u001b[1m\u001b[35mtracing_subscriber::fmt::Layer<S, N, E, W>\u001b[0m\u001b[0m`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: required by a bound in `with`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tracing-subscriber-0.3.19/src/layer/mod.rs:1504:12\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1502\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn with<L>(self, layer: L) -> Layered<L, Self>\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mrequired by a bound in this associated function\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1503\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    where\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1504\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        L: Layer<Self>,\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mrequired by this bound in `__tracing_subscriber_SubscriberExt::with`\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: the full name for the type has been written to '/Users/<USER>/Desktop/logger/target/debug/deps/actlogica_logs-7311386296642d7a.long-type-2831091847738482321.txt'\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: consider using `--verbose` to print the full type name to the console\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the trait bound `Layer<Registry, CustomFormatter, Format, NonBlocking>: Layer<...>` is not satisfied","code":{"code":"E0277","explanation":"You tried to use a type which doesn't implement some trait in a place which\nexpected that trait.\n\nErroneous code example:\n\n```compile_fail,E0277\n// here we declare the Foo trait with a bar method\ntrait Foo {\n    fn bar(&self);\n}\n\n// we now declare a function which takes an object implementing the Foo trait\nfn some_func<T: Foo>(foo: T) {\n    foo.bar();\n}\n\nfn main() {\n    // we now call the method with the i32 type, which doesn't implement\n    // the Foo trait\n    some_func(5i32); // error: the trait bound `i32 : Foo` is not satisfied\n}\n```\n\nIn order to fix this error, verify that the type you're using does implement\nthe trait. Example:\n\n```\ntrait Foo {\n    fn bar(&self);\n}\n\n// we implement the trait on the i32 type\nimpl Foo for i32 {\n    fn bar(&self) {}\n}\n\nfn some_func<T: Foo>(foo: T) {\n    foo.bar(); // we can now use this method since i32 implements the\n               // Foo trait\n}\n\nfn main() {\n    some_func(5i32); // ok!\n}\n```\n\nOr in a generic context, an erroneous code example would look like:\n\n```compile_fail,E0277\nfn some_func<T>(foo: T) {\n    println!(\"{:?}\", foo); // error: the trait `core::fmt::Debug` is not\n                           //        implemented for the type `T`\n}\n\nfn main() {\n    // We now call the method with the i32 type,\n    // which *does* implement the Debug trait.\n    some_func(5i32);\n}\n```\n\nNote that the error here is in the definition of the generic function. Although\nwe only call it with a parameter that does implement `Debug`, the compiler\nstill rejects the function. It must work with all possible input types. In\norder to make this example compile, we need to restrict the generic type we're\naccepting:\n\n```\nuse std::fmt;\n\n// Restrict the input type to types that implement Debug.\nfn some_func<T: fmt::Debug>(foo: T) {\n    println!(\"{:?}\", foo);\n}\n\nfn main() {\n    // Calling the method is still fine, as i32 implements Debug.\n    some_func(5i32);\n\n    // This would fail to compile now:\n    // struct WithoutDebug;\n    // some_func(WithoutDebug);\n}\n```\n\nRust only looks at the signature of the called function, as such it must\nalready specify all requirements that will be used for every type parameter.\n"},"level":"error","spans":[{"file_name":"actlogica-logger/src/setting.rs","byte_start":2234,"byte_end":2260,"line_start":61,"line_end":61,"column_start":53,"column_end":79,"is_primary":true,"text":[{"text":"            tracing::subscriber::set_global_default(subscriber.with(fmt_layer))","highlight_start":53,"highlight_end":79}],"label":"unsatisfied trait bound","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"actlogica-logger/src/setting.rs","byte_start":2194,"byte_end":2233,"line_start":61,"line_end":61,"column_start":13,"column_end":52,"is_primary":false,"text":[{"text":"            tracing::subscriber::set_global_default(subscriber.with(fmt_layer))","highlight_start":13,"highlight_end":52}],"label":"required by a bound introduced by this call","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"the trait `__tracing_subscriber_Layer<Layered<LevelFilter, Registry>>` is not implemented for `tracing_subscriber::fmt::Layer<Registry, CustomFormatter, Format, NonBlocking>`","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"the trait `__tracing_subscriber_Layer<S>` is implemented for `tracing_subscriber::fmt::Layer<S, N, E, W>`","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"required for `Layered<Layer<Registry, CustomFormatter, Format, NonBlocking>, ...>` to implement `tracing::Subscriber`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"required by a bound in `tracing::subscriber::set_global_default`","code":null,"level":"note","spans":[{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tracing-0.1.41/src/subscriber.rs","byte_start":1371,"byte_end":1389,"line_start":39,"line_end":39,"column_start":8,"column_end":26,"is_primary":false,"text":[{"text":"pub fn set_global_default<S>(subscriber: S) -> Result<(), SetGlobalDefaultError>","highlight_start":8,"highlight_end":26}],"label":"required by a bound in this function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tracing-0.1.41/src/subscriber.rs","byte_start":1458,"byte_end":1468,"line_start":41,"line_end":41,"column_start":8,"column_end":18,"is_primary":true,"text":[{"text":"    S: Subscriber + Send + Sync + 'static,","highlight_start":8,"highlight_end":18}],"label":"required by this bound in `set_global_default`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"the full name for the type has been written to '/Users/<USER>/Desktop/logger/target/debug/deps/actlogica_logs-7311386296642d7a.long-type-2831091847738482321.txt'","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"consider using `--verbose` to print the full type name to the console","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0277]\u001b[0m\u001b[0m\u001b[1m: the trait bound `Layer<Registry, CustomFormatter, Format, NonBlocking>: Layer<...>` is not satisfied\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mactlogica-logger/src/setting.rs:61:53\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m61\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            tracing::subscriber::set_global_default(subscriber.with(fmt_layer))\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m---------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9munsatisfied trait bound\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mrequired by a bound introduced by this call\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: the trait `__tracing_subscriber_Layer<Layered<LevelFilter, Registry>>` is not implemented for `tracing_subscriber::fmt::Layer<Registry, CustomFormatter, Format, NonBlocking>`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: the trait `__tracing_subscriber_Layer<S>` \u001b[0m\u001b[0m\u001b[1m\u001b[35mis\u001b[0m\u001b[0m implemented for `\u001b[0m\u001b[0m\u001b[1m\u001b[35mtracing_subscriber::fmt::Layer<S, N, E, W>\u001b[0m\u001b[0m`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: required for `Layered<Layer<Registry, CustomFormatter, Format, NonBlocking>, ...>` to implement `tracing::Subscriber`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: required by a bound in `tracing::subscriber::set_global_default`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tracing-0.1.41/src/subscriber.rs:41:8\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m39\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub fn set_global_default<S>(subscriber: S) -> Result<(), SetGlobalDefaultError>\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mrequired by a bound in this function\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m40\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mwhere\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m41\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    S: Subscriber + Send + Sync + 'static,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mrequired by this bound in `set_global_default`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: the full name for the type has been written to '/Users/<USER>/Desktop/logger/target/debug/deps/actlogica_logs-7311386296642d7a.long-type-2831091847738482321.txt'\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: consider using `--verbose` to print the full type name to the console\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"mismatched types","code":{"code":"E0308","explanation":"Expected type did not match the received type.\n\nErroneous code examples:\n\n```compile_fail,E0308\nfn plus_one(x: i32) -> i32 {\n    x + 1\n}\n\nplus_one(\"Not a number\");\n//       ^^^^^^^^^^^^^^ expected `i32`, found `&str`\n\nif \"Not a bool\" {\n// ^^^^^^^^^^^^ expected `bool`, found `&str`\n}\n\nlet x: f32 = \"Not a float\";\n//     ---   ^^^^^^^^^^^^^ expected `f32`, found `&str`\n//     |\n//     expected due to this\n```\n\nThis error occurs when an expression was used in a place where the compiler\nexpected an expression of a different type. It can occur in several cases, the\nmost common being when calling a function and passing an argument which has a\ndifferent type than the matching type in the function declaration.\n"},"level":"error","spans":[{"file_name":"actlogica-logger/src/utils.rs","byte_start":1370,"byte_end":1629,"line_start":46,"line_end":54,"column_start":5,"column_end":11,"is_primary":true,"text":[{"text":"    tracing_subscriber::fmt::layer()","highlight_start":5,"highlight_end":37},{"text":"        .json()","highlight_start":1,"highlight_end":16},{"text":"        .without_time()","highlight_start":1,"highlight_end":24},{"text":"        .with_level(true)","highlight_start":1,"highlight_end":26},{"text":"        .with_current_span(true)","highlight_start":1,"highlight_end":33},{"text":"        .event_format(CustomFormatter {","highlight_start":1,"highlight_end":40},{"text":"            service_name: service_name.to_string(),","highlight_start":1,"highlight_end":52},{"text":"            output_type,","highlight_start":1,"highlight_end":25},{"text":"        })","highlight_start":1,"highlight_end":11}],"label":"expected `Layer<Registry, CustomFormatter>`, found `Layer<_, JsonFields, CustomFormatter>`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"actlogica-logger/src/utils.rs","byte_start":1286,"byte_end":1363,"line_start":45,"line_end":45,"column_start":6,"column_end":83,"is_primary":false,"text":[{"text":") -> tracing_subscriber::fmt::Layer<tracing_subscriber::Registry, CustomFormatter> {","highlight_start":6,"highlight_end":83}],"label":"expected `tracing_subscriber::fmt::Layer<Registry, CustomFormatter>` because of return type","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"expected struct `tracing_subscriber::fmt::Layer<Registry, CustomFormatter, Format>`\n   found struct `tracing_subscriber::fmt::Layer<_, JsonFields, CustomFormatter>`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0308]\u001b[0m\u001b[0m\u001b[1m: mismatched types\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mactlogica-logger/src/utils.rs:46:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m45\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m) -> tracing_subscriber::fmt::Layer<tracing_subscriber::Registry, CustomFormatter> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----------------------------------------------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mexpected `tracing_subscriber::fmt::Layer<Registry, CustomFormatter>` because of return type\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m46\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m/\u001b[0m\u001b[0m \u001b[0m\u001b[0m    tracing_subscriber::fmt::layer()\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m47\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        .json()\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m48\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        .without_time()\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m49\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        .with_level(true)\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m53\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            output_type,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m54\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        })\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|__________^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mexpected `Layer<Registry, CustomFormatter>`, found `Layer<_, JsonFields, CustomFormatter>`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: expected struct `tracing_subscriber::fmt::Layer<\u001b[0m\u001b[0m\u001b[1m\u001b[35mRegistry\u001b[0m\u001b[0m, \u001b[0m\u001b[0m\u001b[1m\u001b[35mCustomFormatter\u001b[0m\u001b[0m, \u001b[0m\u001b[0m\u001b[1m\u001b[35mFormat\u001b[0m\u001b[0m>`\u001b[0m\n\u001b[0m              found struct `tracing_subscriber::fmt::Layer<\u001b[0m\u001b[0m\u001b[1m\u001b[35m_\u001b[0m\u001b[0m, \u001b[0m\u001b[0m\u001b[1m\u001b[35mJsonFields\u001b[0m\u001b[0m, \u001b[0m\u001b[0m\u001b[1m\u001b[35mCustomFormatter\u001b[0m\u001b[0m>`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"aborting due to 8 previous errors","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: aborting due to 8 previous errors\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"Some errors have detailed explanations: E0121, E0277, E0308.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1mSome errors have detailed explanations: E0121, E0277, E0308.\u001b[0m\n"}
{"$message_type":"diagnostic","message":"For more information about an error, try `rustc --explain E0121`.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1mFor more information about an error, try `rustc --explain E0121`.\u001b[0m\n"}
