{"rustc": 5148484765029645790, "features": "[]", "declared_features": "[]", "target": 14840160241370058439, "profile": 6675295047989516842, "path": 18017183740903701435, "deps": [[1760623714118191065, "dotenv", false, 11455478925194274830], [5138218615291878843, "tokio", false, 16095950932233218745], [6547980334806251551, "chrono", false, 14712236072124772675], [6601542651617637986, "base62", false, 18037826295709398880], [8269115081296425610, "uuid", false, 16954997910838863704], [8606274917505247608, "tracing", false, 5736389644838325727], [9689903380558560274, "serde", false, 1133203805763328567], [11033263105862272874, "tracing_core", false, 8128111630098495462], [11594979262886006466, "tracing_appender", false, 6257909630723721709], [12371715326520714823, "rdkafka", false, 3475770775282470213], [15367738274754116744, "serde_json", false, 7223384620422996608], [16230660778393187092, "tracing_subscriber", false, 8238257718453700001]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/actlogica_logs-9112a1cf85c114e5/dep-lib-actlogica_logs", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}