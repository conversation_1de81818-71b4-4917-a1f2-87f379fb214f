{"rustc": 5148484765029645790, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[9070360545695802481, "build_script_main", false, 3520640054807191855]], "local": [{"RerunIfChanged": {"output": "debug/build/openssl-sys-762330f5c5146307/output", "paths": ["/usr/local/opt/openssl@3/include/openssl", "build/expando.c"]}}, {"RerunIfEnvChanged": {"var": "X86_64_APPLE_DARWIN_OPENSSL_LIB_DIR", "val": null}}, {"RerunIfEnvChanged": {"var": "OPENSSL_LIB_DIR", "val": null}}, {"RerunIfEnvChanged": {"var": "X86_64_APPLE_DARWIN_OPENSSL_INCLUDE_DIR", "val": null}}, {"RerunIfEnvChanged": {"var": "OPENSSL_INCLUDE_DIR", "val": null}}, {"RerunIfEnvChanged": {"var": "X86_64_APPLE_DARWIN_OPENSSL_DIR", "val": null}}, {"RerunIfEnvChanged": {"var": "OPENSSL_DIR", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_x86_64-apple-darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_x86_64_apple_darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_ENABLE_DEBUG_OUTPUT", "val": null}}, {"RerunIfEnvChanged": {"var": "CRATE_CC_NO_DEFAULTS", "val": null}}, {"RerunIfEnvChanged": {"var": "MACOSX_DEPLOYMENT_TARGET", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_x86_64_apple_darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_x86_64-apple-darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "X86_64_APPLE_DARWIN_OPENSSL_LIBS", "val": null}}, {"RerunIfEnvChanged": {"var": "OPENSSL_LIBS", "val": null}}, {"RerunIfEnvChanged": {"var": "X86_64_APPLE_DARWIN_OPENSSL_STATIC", "val": null}}, {"RerunIfEnvChanged": {"var": "OPENSSL_STATIC", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}