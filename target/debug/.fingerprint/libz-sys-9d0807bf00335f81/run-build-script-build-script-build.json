{"rustc": 5148484765029645790, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[17022423707615322322, "build_script_build", false, 1387271922370747264]], "local": [{"RerunIfChanged": {"output": "debug/build/libz-sys-9d0807bf00335f81/output", "paths": ["build.rs", "zng/cmake.rs", "zng/cc.rs"]}}, {"RerunIfEnvChanged": {"var": "LIBZ_SYS_STATIC", "val": null}}, {"RerunIfEnvChanged": {"var": "ZLIB_NO_PKG_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_x86_64-apple-darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_x86_64_apple_darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_PKG_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "ZLIB_STATIC", "val": null}}, {"RerunIfEnvChanged": {"var": "ZLIB_DYNAMIC", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_ALL_STATIC", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_ALL_DYNAMIC", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_PATH_x86_64-apple-darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_PATH_x86_64_apple_darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_PKG_CONFIG_PATH", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_PATH", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_LIBDIR_x86_64-apple-darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_LIBDIR_x86_64_apple_darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_PKG_CONFIG_LIBDIR", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_LIBDIR", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_SYSROOT_DIR_x86_64-apple-darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_SYSROOT_DIR_x86_64_apple_darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_PKG_CONFIG_SYSROOT_DIR", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_SYSROOT_DIR", "val": null}}, {"RerunIfEnvChanged": {"var": "ZLIB_STATIC", "val": null}}, {"RerunIfEnvChanged": {"var": "ZLIB_DYNAMIC", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_ALL_STATIC", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_ALL_DYNAMIC", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_x86_64-apple-darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_x86_64_apple_darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_PKG_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "ZLIB_STATIC", "val": null}}, {"RerunIfEnvChanged": {"var": "ZLIB_DYNAMIC", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_ALL_STATIC", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_ALL_DYNAMIC", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_PATH_x86_64-apple-darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_PATH_x86_64_apple_darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_PKG_CONFIG_PATH", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_PATH", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_LIBDIR_x86_64-apple-darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_LIBDIR_x86_64_apple_darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_PKG_CONFIG_LIBDIR", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_LIBDIR", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_SYSROOT_DIR_x86_64-apple-darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_SYSROOT_DIR_x86_64_apple_darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_PKG_CONFIG_SYSROOT_DIR", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_SYSROOT_DIR", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_x86_64-apple-darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_x86_64_apple_darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_ENABLE_DEBUG_OUTPUT", "val": null}}, {"RerunIfEnvChanged": {"var": "CRATE_CC_NO_DEFAULTS", "val": null}}, {"RerunIfEnvChanged": {"var": "MACOSX_DEPLOYMENT_TARGET", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_x86_64_apple_darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_x86_64-apple-darwin", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}