Building and linking librdkafka statically
Running command: "/Users/<USER>/Desktop/logger/target/debug/build/rdkafka-sys-2cd0cac3e7b46bee/out/configure --enable-ssl --disable-gssapi --disable-curl --disable-zstd --disable-lz4-ext" in dir: /Users/<USER>/Desktop/logger/target/debug/build/rdkafka-sys-2cd0cac3e7b46bee/out
Running command: "make libs" in dir: /Users/<USER>/Desktop/logger/target/debug/build/rdkafka-sys-2cd0cac3e7b46bee/out
rdkafka_broker.c:799:13: warning: variable 'idx' set but not used [-Wunused-but-set-variable]
  799 |         int idx = -1;
      |             ^
rdkafka_request.c:2940:29: warning: format specifies type 'long' but the argument has type 'int64_t' (aka 'long long') [-Wformat]
 2939 |                             "Received session lifetime %ld ms from broker",
      |                                                        ~~~
      |                                                        %lld
 2940 |                             session_lifetime_ms);
      |                             ^~~~~~~~~~~~~~~~~~~
./rdkafka_int.h:886:66: note: expanded from macro 'rd_kafka_dbg'
  886 |                                       (RD_KAFKA_DBG_##ctx), fac, __VA_ARGS__); \
      |                                                                  ^~~~~~~~~~~
rdkafka_cgrp.c:1360:13: warning: variable 'not_revoking' set but not used [-Wunused-but-set-variable]
 1360 |         int not_revoking   = 0;
      |             ^
1 warning generated.
rdbuf.c:908:16: warning: variable 'remains' set but not used [-Wunused-but-set-variable]
  908 |         size_t remains = size;
      |                ^
rdmap.c:482:13: warning: variable 'fails' set but not used [-Wunused-but-set-variable]
  482 |         int fails = 0;
      |             ^
1 warning generated.
1 warning generated.
1 warning generated.
1 warning generated.
snappy.c:1440:9: warning: variable 'written' set but not used [-Wunused-but-set-variable]
 1440 |         size_t written = 0;
      |                ^
1 warning generated.
