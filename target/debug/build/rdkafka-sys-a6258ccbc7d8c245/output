Configuring librdkafka
using cache file config.cache
checking for OS or distribution... ok (osx)
checking for C compiler from CC env... failed
checking for gcc (by command)... ok
checking for C++ compiler from CXX env... failed
checking for C++ compiler (g++)... ok
checking executable ld... ok
checking executable nm... ok
checking executable objdump... ok
checking executable strip... ok
checking executable libtool... ok
checking executable ranlib... ok
checking for debug symbols compiler flag (-g...)... ok
checking for pkgconfig (by command)... ok
checking for install (by command)... failed
checking for GNU ar... failed (disable)
checking for PIC (by compile)... ok
checking for GNU-compatible linker options... failed
checking for OSX linker options... ok
checking for GNU linker-script ld flag... failed
checking for Solaris linker-script ld flag... failed (ignore)
checking for __atomic_32 (by compile)... ok
checking for __atomic_64 (by compile)... ok
checking for socket (by compile)... ok
parsing version '0x020300ff'... ok (2.3.0)
checking for librt (by pkg-config)... failed
checking for librt (by compile)... failed
checking for libpthread (by pkg-config)... failed
checking for libpthread (by compile)... ok
checking for c11threads (by pkg-config)... failed
checking for c11threads (by compile)... failed (disable)
checking for libdl (by pkg-config)... failed
checking for libdl (by compile)... ok
checking for zlib (by pkg-config)... ok
checking for libm (by pkg-config)... failed
checking for libm (by compile)... ok
checking for syslog (by compile)... ok
checking for rapidjson (by compile)... failed (disable)
checking for crc32chw (by compile)... ok
checking for regex (by compile)... ok
checking for rand_r (by compile)... ok
checking for strndup (by compile)... ok
checking for strlcpy (by compile)... ok
checking for strerror_r (by compile)... ok
checking for strcasestr (by compile)... ok
checking for pthread_setname_gnu (by compile)... failed (disable)
checking for pthread_setname_darwin (by compile)... ok
checking for nm (by env NM)... ok (cached)
checking for getrusage (by compile)... ok
Generated Makefile.config
Generated config.h

Configuration summary:
  prefix                   /usr/local
  MKL_DISTRO               osx
  SOLIB_EXT                .dylib
  ARCH                     x86_64
  CPU                      generic
  GEN_PKG_CONFIG           y
  MKL_APP_NAME             librdkafka
  MKL_APP_DESC_ONELINE     The Apache Kafka C/C++ library
  CC                       gcc
  CXX                      g++
  LD                       ld
  NM                       nm
  OBJDUMP                  objdump
  STRIP                    strip
  LIBTOOL                  libtool
  RANLIB                   ranlib
  CPPFLAGS                 -gstrict-dwarf -O2 -fPIC -Wall -Wsign-compare -Wfloat-equal -Wpointer-arith -Wcast-align
  PKG_CONFIG               pkg-config
  INSTALL                  /usr/bin/install
  HAS_GNU_AR               n
  LIB_LDFLAGS              -shared -dynamiclib -Wl,-install_name,$(DESTDIR)$(libdir)/$(LIBFILENAME)
  RDKAFKA_VERSION_STR      2.3.0
  MKL_APP_VERSION          2.3.0
  LIBS                     -lm -lz -ldl -lpthread
  MKL_PKGCONFIG_LIBS_PRIVATE -lm -ldl -lpthread
  MKL_PKGCONFIG_REQUIRES_PRIVATE zlib
  CFLAGS                   
  MKL_PKGCONFIG_REQUIRES   zlib
  CXXFLAGS                 -Wno-non-virtual-dtor
  SYMDUMPER                $(NM) -g
  MKL_DYNAMIC_LIBS         -lm -lz -ldl -lpthread
  exec_prefix              /usr/local
  bindir                   /usr/local/bin
  sbindir                  /usr/local/sbin
  libexecdir               /usr/local/libexec
  datadir                  /usr/local/share
  sysconfdir               /usr/local/etc
  sharedstatedir           /usr/local/com
  localstatedir            /usr/local/var
  runstatedir              /usr/local/var/run
  libdir                   /usr/local/lib
  includedir               /usr/local/include
  infodir                  /usr/local/info
  mandir                   /usr/local/man
  BUILT_WITH               GCC GXX PKGCONFIG OSXLD LIBDL PLUGINS ZLIB HDRHISTOGRAM SYSLOG SNAPPY SOCKEM CRC32C_HW
Generated config.cache

Now type 'make' to build
Compiling librdkafka
gcc -MD -MP -gstrict-dwarf -O2 -fPIC -Wall -Wsign-compare -Wfloat-equal -Wpointer-arith -Wcast-align    -c rdkafka.c -o rdkafka.o
gcc -MD -MP -gstrict-dwarf -O2 -fPIC -Wall -Wsign-compare -Wfloat-equal -Wpointer-arith -Wcast-align    -c rdkafka_broker.c -o rdkafka_broker.o
gcc -MD -MP -gstrict-dwarf -O2 -fPIC -Wall -Wsign-compare -Wfloat-equal -Wpointer-arith -Wcast-align    -c rdkafka_msg.c -o rdkafka_msg.o
gcc -MD -MP -gstrict-dwarf -O2 -fPIC -Wall -Wsign-compare -Wfloat-equal -Wpointer-arith -Wcast-align    -c rdkafka_topic.c -o rdkafka_topic.o
gcc -MD -MP -gstrict-dwarf -O2 -fPIC -Wall -Wsign-compare -Wfloat-equal -Wpointer-arith -Wcast-align    -c rdkafka_conf.c -o rdkafka_conf.o
gcc -MD -MP -gstrict-dwarf -O2 -fPIC -Wall -Wsign-compare -Wfloat-equal -Wpointer-arith -Wcast-align    -c rdkafka_timer.c -o rdkafka_timer.o
gcc -MD -MP -gstrict-dwarf -O2 -fPIC -Wall -Wsign-compare -Wfloat-equal -Wpointer-arith -Wcast-align    -c rdkafka_offset.c -o rdkafka_offset.o
gcc -MD -MP -gstrict-dwarf -O2 -fPIC -Wall -Wsign-compare -Wfloat-equal -Wpointer-arith -Wcast-align    -c rdkafka_transport.c -o rdkafka_transport.o
gcc -MD -MP -gstrict-dwarf -O2 -fPIC -Wall -Wsign-compare -Wfloat-equal -Wpointer-arith -Wcast-align    -c rdkafka_buf.c -o rdkafka_buf.o
gcc -MD -MP -gstrict-dwarf -O2 -fPIC -Wall -Wsign-compare -Wfloat-equal -Wpointer-arith -Wcast-align    -c rdkafka_queue.c -o rdkafka_queue.o
gcc -MD -MP -gstrict-dwarf -O2 -fPIC -Wall -Wsign-compare -Wfloat-equal -Wpointer-arith -Wcast-align    -c rdkafka_op.c -o rdkafka_op.o
gcc -MD -MP -gstrict-dwarf -O2 -fPIC -Wall -Wsign-compare -Wfloat-equal -Wpointer-arith -Wcast-align    -c rdkafka_request.c -o rdkafka_request.o
gcc -MD -MP -gstrict-dwarf -O2 -fPIC -Wall -Wsign-compare -Wfloat-equal -Wpointer-arith -Wcast-align    -c rdkafka_cgrp.c -o rdkafka_cgrp.o
gcc -MD -MP -gstrict-dwarf -O2 -fPIC -Wall -Wsign-compare -Wfloat-equal -Wpointer-arith -Wcast-align    -c rdkafka_pattern.c -o rdkafka_pattern.o
gcc -MD -MP -gstrict-dwarf -O2 -fPIC -Wall -Wsign-compare -Wfloat-equal -Wpointer-arith -Wcast-align    -c rdkafka_partition.c -o rdkafka_partition.o
gcc -MD -MP -gstrict-dwarf -O2 -fPIC -Wall -Wsign-compare -Wfloat-equal -Wpointer-arith -Wcast-align    -c rdkafka_subscription.c -o rdkafka_subscription.o
gcc -MD -MP -gstrict-dwarf -O2 -fPIC -Wall -Wsign-compare -Wfloat-equal -Wpointer-arith -Wcast-align    -c rdkafka_assignment.c -o rdkafka_assignment.o
gcc -MD -MP -gstrict-dwarf -O2 -fPIC -Wall -Wsign-compare -Wfloat-equal -Wpointer-arith -Wcast-align    -c rdkafka_assignor.c -o rdkafka_assignor.o
gcc -MD -MP -gstrict-dwarf -O2 -fPIC -Wall -Wsign-compare -Wfloat-equal -Wpointer-arith -Wcast-align    -c rdkafka_range_assignor.c -o rdkafka_range_assignor.o
gcc -MD -MP -gstrict-dwarf -O2 -fPIC -Wall -Wsign-compare -Wfloat-equal -Wpointer-arith -Wcast-align    -c rdkafka_roundrobin_assignor.c -o rdkafka_roundrobin_assignor.o
gcc -MD -MP -gstrict-dwarf -O2 -fPIC -Wall -Wsign-compare -Wfloat-equal -Wpointer-arith -Wcast-align    -c rdkafka_sticky_assignor.c -o rdkafka_sticky_assignor.o
gcc -MD -MP -gstrict-dwarf -O2 -fPIC -Wall -Wsign-compare -Wfloat-equal -Wpointer-arith -Wcast-align    -c rdkafka_feature.c -o rdkafka_feature.o
gcc -MD -MP -gstrict-dwarf -O2 -fPIC -Wall -Wsign-compare -Wfloat-equal -Wpointer-arith -Wcast-align    -c rdcrc32.c -o rdcrc32.o
gcc -MD -MP -gstrict-dwarf -O2 -fPIC -Wall -Wsign-compare -Wfloat-equal -Wpointer-arith -Wcast-align    -c crc32c.c -o crc32c.o
gcc -MD -MP -gstrict-dwarf -O2 -fPIC -Wall -Wsign-compare -Wfloat-equal -Wpointer-arith -Wcast-align    -c rdmurmur2.c -o rdmurmur2.o
gcc -MD -MP -gstrict-dwarf -O2 -fPIC -Wall -Wsign-compare -Wfloat-equal -Wpointer-arith -Wcast-align    -c rdfnv1a.c -o rdfnv1a.o
gcc -MD -MP -gstrict-dwarf -O2 -fPIC -Wall -Wsign-compare -Wfloat-equal -Wpointer-arith -Wcast-align    -c rdaddr.c -o rdaddr.o
gcc -MD -MP -gstrict-dwarf -O2 -fPIC -Wall -Wsign-compare -Wfloat-equal -Wpointer-arith -Wcast-align    -c rdrand.c -o rdrand.o
gcc -MD -MP -gstrict-dwarf -O2 -fPIC -Wall -Wsign-compare -Wfloat-equal -Wpointer-arith -Wcast-align    -c rdlist.c -o rdlist.o
gcc -MD -MP -gstrict-dwarf -O2 -fPIC -Wall -Wsign-compare -Wfloat-equal -Wpointer-arith -Wcast-align    -c tinycthread.c -o tinycthread.o
gcc -MD -MP -gstrict-dwarf -O2 -fPIC -Wall -Wsign-compare -Wfloat-equal -Wpointer-arith -Wcast-align    -c tinycthread_extra.c -o tinycthread_extra.o
gcc -MD -MP -gstrict-dwarf -O2 -fPIC -Wall -Wsign-compare -Wfloat-equal -Wpointer-arith -Wcast-align    -c rdlog.c -o rdlog.o
gcc -MD -MP -gstrict-dwarf -O2 -fPIC -Wall -Wsign-compare -Wfloat-equal -Wpointer-arith -Wcast-align    -c rdstring.c -o rdstring.o
gcc -MD -MP -gstrict-dwarf -O2 -fPIC -Wall -Wsign-compare -Wfloat-equal -Wpointer-arith -Wcast-align    -c rdkafka_event.c -o rdkafka_event.o
gcc -MD -MP -gstrict-dwarf -O2 -fPIC -Wall -Wsign-compare -Wfloat-equal -Wpointer-arith -Wcast-align    -c rdkafka_metadata.c -o rdkafka_metadata.o
gcc -MD -MP -gstrict-dwarf -O2 -fPIC -Wall -Wsign-compare -Wfloat-equal -Wpointer-arith -Wcast-align    -c rdregex.c -o rdregex.o
gcc -MD -MP -gstrict-dwarf -O2 -fPIC -Wall -Wsign-compare -Wfloat-equal -Wpointer-arith -Wcast-align    -c rdports.c -o rdports.o
gcc -MD -MP -gstrict-dwarf -O2 -fPIC -Wall -Wsign-compare -Wfloat-equal -Wpointer-arith -Wcast-align    -c rdkafka_metadata_cache.c -o rdkafka_metadata_cache.o
gcc -MD -MP -gstrict-dwarf -O2 -fPIC -Wall -Wsign-compare -Wfloat-equal -Wpointer-arith -Wcast-align    -c rdavl.c -o rdavl.o
gcc -MD -MP -gstrict-dwarf -O2 -fPIC -Wall -Wsign-compare -Wfloat-equal -Wpointer-arith -Wcast-align    -c rdkafka_sasl.c -o rdkafka_sasl.o
gcc -MD -MP -gstrict-dwarf -O2 -fPIC -Wall -Wsign-compare -Wfloat-equal -Wpointer-arith -Wcast-align    -c rdkafka_sasl_plain.c -o rdkafka_sasl_plain.o
gcc -MD -MP -gstrict-dwarf -O2 -fPIC -Wall -Wsign-compare -Wfloat-equal -Wpointer-arith -Wcast-align    -c rdkafka_interceptor.c -o rdkafka_interceptor.o
gcc -MD -MP -gstrict-dwarf -O2 -fPIC -Wall -Wsign-compare -Wfloat-equal -Wpointer-arith -Wcast-align    -c rdkafka_msgset_writer.c -o rdkafka_msgset_writer.o
gcc -MD -MP -gstrict-dwarf -O2 -fPIC -Wall -Wsign-compare -Wfloat-equal -Wpointer-arith -Wcast-align    -c rdkafka_msgset_reader.c -o rdkafka_msgset_reader.o
gcc -MD -MP -gstrict-dwarf -O2 -fPIC -Wall -Wsign-compare -Wfloat-equal -Wpointer-arith -Wcast-align    -c rdkafka_header.c -o rdkafka_header.o
gcc -MD -MP -gstrict-dwarf -O2 -fPIC -Wall -Wsign-compare -Wfloat-equal -Wpointer-arith -Wcast-align    -c rdkafka_admin.c -o rdkafka_admin.o
gcc -MD -MP -gstrict-dwarf -O2 -fPIC -Wall -Wsign-compare -Wfloat-equal -Wpointer-arith -Wcast-align    -c rdkafka_aux.c -o rdkafka_aux.o
gcc -MD -MP -gstrict-dwarf -O2 -fPIC -Wall -Wsign-compare -Wfloat-equal -Wpointer-arith -Wcast-align    -c rdkafka_background.c -o rdkafka_background.o
gcc -MD -MP -gstrict-dwarf -O2 -fPIC -Wall -Wsign-compare -Wfloat-equal -Wpointer-arith -Wcast-align    -c rdkafka_idempotence.c -o rdkafka_idempotence.o
gcc -MD -MP -gstrict-dwarf -O2 -fPIC -Wall -Wsign-compare -Wfloat-equal -Wpointer-arith -Wcast-align    -c rdkafka_cert.c -o rdkafka_cert.o
gcc -MD -MP -gstrict-dwarf -O2 -fPIC -Wall -Wsign-compare -Wfloat-equal -Wpointer-arith -Wcast-align    -c rdkafka_txnmgr.c -o rdkafka_txnmgr.o
gcc -MD -MP -gstrict-dwarf -O2 -fPIC -Wall -Wsign-compare -Wfloat-equal -Wpointer-arith -Wcast-align    -c rdkafka_coord.c -o rdkafka_coord.o
gcc -MD -MP -gstrict-dwarf -O2 -fPIC -Wall -Wsign-compare -Wfloat-equal -Wpointer-arith -Wcast-align    -c rdbase64.c -o rdbase64.o
gcc -MD -MP -gstrict-dwarf -O2 -fPIC -Wall -Wsign-compare -Wfloat-equal -Wpointer-arith -Wcast-align    -c rdvarint.c -o rdvarint.o
gcc -MD -MP -gstrict-dwarf -O2 -fPIC -Wall -Wsign-compare -Wfloat-equal -Wpointer-arith -Wcast-align    -c rdbuf.c -o rdbuf.o
gcc -MD -MP -gstrict-dwarf -O2 -fPIC -Wall -Wsign-compare -Wfloat-equal -Wpointer-arith -Wcast-align    -c rdmap.c -o rdmap.o
gcc -MD -MP -gstrict-dwarf -O2 -fPIC -Wall -Wsign-compare -Wfloat-equal -Wpointer-arith -Wcast-align    -c rdunittest.c -o rdunittest.o
gcc -MD -MP -gstrict-dwarf -O2 -fPIC -Wall -Wsign-compare -Wfloat-equal -Wpointer-arith -Wcast-align    -c rdkafka_mock.c -o rdkafka_mock.o
gcc -MD -MP -gstrict-dwarf -O2 -fPIC -Wall -Wsign-compare -Wfloat-equal -Wpointer-arith -Wcast-align    -c rdkafka_mock_handlers.c -o rdkafka_mock_handlers.o
gcc -MD -MP -gstrict-dwarf -O2 -fPIC -Wall -Wsign-compare -Wfloat-equal -Wpointer-arith -Wcast-align    -c rdkafka_mock_cgrp.c -o rdkafka_mock_cgrp.o
gcc -MD -MP -gstrict-dwarf -O2 -fPIC -Wall -Wsign-compare -Wfloat-equal -Wpointer-arith -Wcast-align    -c rdkafka_error.c -o rdkafka_error.o
gcc -MD -MP -gstrict-dwarf -O2 -fPIC -Wall -Wsign-compare -Wfloat-equal -Wpointer-arith -Wcast-align    -c rdkafka_fetcher.c -o rdkafka_fetcher.o
gcc -MD -MP -gstrict-dwarf -O2 -fPIC -Wall -Wsign-compare -Wfloat-equal -Wpointer-arith -Wcast-align    -c snappy.c -o snappy.o
gcc -MD -MP -gstrict-dwarf -O2 -fPIC -Wall -Wsign-compare -Wfloat-equal -Wpointer-arith -Wcast-align    -c rdgz.c -o rdgz.o
gcc -MD -MP -gstrict-dwarf -O2 -fPIC -Wall -Wsign-compare -Wfloat-equal -Wpointer-arith -Wcast-align    -c rdhdrhistogram.c -o rdhdrhistogram.o
gcc -MD -MP -gstrict-dwarf -O2 -fPIC -Wall -Wsign-compare -Wfloat-equal -Wpointer-arith -Wcast-align    -c rdkafka_lz4.c -o rdkafka_lz4.o
gcc -MD -MP -gstrict-dwarf -O2 -fPIC -Wall -Wsign-compare -Wfloat-equal -Wpointer-arith -Wcast-align    -O3 -c rdxxhash.c -o rdxxhash.o
gcc -MD -MP -gstrict-dwarf -O2 -fPIC -Wall -Wsign-compare -Wfloat-equal -Wpointer-arith -Wcast-align    -O3 -c lz4frame.c -o lz4frame.o
gcc -MD -MP -gstrict-dwarf -O2 -fPIC -Wall -Wsign-compare -Wfloat-equal -Wpointer-arith -Wcast-align    -c rddl.c -o rddl.o
gcc -MD -MP -gstrict-dwarf -O2 -fPIC -Wall -Wsign-compare -Wfloat-equal -Wpointer-arith -Wcast-align    -c rdkafka_plugin.c -o rdkafka_plugin.o
Generating pkg-config file rdkafka.pc
Creating static library librdkafka.a
Creating shared library librdkafka.1.dylib
ar rcs librdkafka.a rdkafka.o rdkafka_broker.o rdkafka_msg.o rdkafka_topic.o rdkafka_conf.o rdkafka_timer.o rdkafka_offset.o rdkafka_transport.o rdkafka_buf.o rdkafka_queue.o rdkafka_op.o rdkafka_request.o rdkafka_cgrp.o rdkafka_pattern.o rdkafka_partition.o rdkafka_subscription.o rdkafka_assignment.o rdkafka_assignor.o rdkafka_range_assignor.o rdkafka_roundrobin_assignor.o rdkafka_sticky_assignor.o rdkafka_feature.o rdcrc32.o crc32c.o rdmurmur2.o rdfnv1a.o cJSON.o rdaddr.o rdrand.o rdlist.o tinycthread.o tinycthread_extra.o rdlog.o rdstring.o rdkafka_event.o rdkafka_metadata.o rdregex.o rdports.o rdkafka_metadata_cache.o rdavl.o rdkafka_sasl.o rdkafka_sasl_plain.o rdkafka_interceptor.o rdkafka_msgset_writer.o rdkafka_msgset_reader.o rdkafka_header.o rdkafka_admin.o rdkafka_aux.o rdkafka_background.o rdkafka_idempotence.o rdkafka_cert.o rdkafka_txnmgr.o rdkafka_coord.o rdbase64.o rdvarint.o rdbuf.o rdmap.o rdunittest.o rdkafka_mock.o rdkafka_mock_handlers.o rdkafka_mock_cgrp.o rdkafka_error.o rdkafka_fetcher.o snappy.o rdgz.o rdhdrhistogram.o rdkafka_lz4.o rdxxhash.o lz4.o lz4frame.o lz4hc.o rddl.o rdkafka_plugin.o
gcc  -shared -dynamiclib -Wl,-install_name,/usr/local/lib/librdkafka.1.dylib rdkafka.o rdkafka_broker.o rdkafka_msg.o rdkafka_topic.o rdkafka_conf.o rdkafka_timer.o rdkafka_offset.o rdkafka_transport.o rdkafka_buf.o rdkafka_queue.o rdkafka_op.o rdkafka_request.o rdkafka_cgrp.o rdkafka_pattern.o rdkafka_partition.o rdkafka_subscription.o rdkafka_assignment.o rdkafka_assignor.o rdkafka_range_assignor.o rdkafka_roundrobin_assignor.o rdkafka_sticky_assignor.o rdkafka_feature.o rdcrc32.o crc32c.o rdmurmur2.o rdfnv1a.o cJSON.o rdaddr.o rdrand.o rdlist.o tinycthread.o tinycthread_extra.o rdlog.o rdstring.o rdkafka_event.o rdkafka_metadata.o rdregex.o rdports.o rdkafka_metadata_cache.o rdavl.o rdkafka_sasl.o rdkafka_sasl_plain.o rdkafka_interceptor.o rdkafka_msgset_writer.o rdkafka_msgset_reader.o rdkafka_header.o rdkafka_admin.o rdkafka_aux.o rdkafka_background.o rdkafka_idempotence.o rdkafka_cert.o rdkafka_txnmgr.o rdkafka_coord.o rdbase64.o rdvarint.o rdbuf.o rdmap.o rdunittest.o rdkafka_mock.o rdkafka_mock_handlers.o rdkafka_mock_cgrp.o rdkafka_error.o rdkafka_fetcher.o snappy.o rdgz.o rdhdrhistogram.o rdkafka_lz4.o rdxxhash.o lz4.o lz4frame.o lz4hc.o rddl.o rdkafka_plugin.o -o librdkafka.1.dylib -lm -lz -ldl -lpthread
cp librdkafka.1.dylib librdkafka-dbg.1.dylib
cp librdkafka.a librdkafka-dbg.a
Creating librdkafka.dylib symlink
rm -f "librdkafka.dylib" && ln -s "librdkafka.1.dylib" "librdkafka.dylib"
WARNING: librdkafka-static.a: No static libraries available/enabled for inclusion in self-contained static library librdkafka-static.a: this library will be identical to librdkafka.a
WARNING: librdkafka-static.a: The following libraries were not available as static libraries and need to be linked dynamically: -lm -lz -ldl -lpthread
cp librdkafka.a librdkafka-static.a
cp librdkafka-dbg.a librdkafka-static-dbg.a
cp librdkafka-static.a librdkafka-static-dbg.a
Generating pkg-config file rdkafka-static.pc
Checking librdkafka integrity
librdkafka.1.dylib             OK
librdkafka.a                   OK
Symbol visibility              OK
g++ -MD -MP -gstrict-dwarf -O2 -fPIC -Wall -Wsign-compare -Wfloat-equal -Wpointer-arith -Wcast-align -Wno-non-virtual-dtor -c RdKafka.cpp -o RdKafka.o
g++ -MD -MP -gstrict-dwarf -O2 -fPIC -Wall -Wsign-compare -Wfloat-equal -Wpointer-arith -Wcast-align -Wno-non-virtual-dtor -c ConfImpl.cpp -o ConfImpl.o
g++ -MD -MP -gstrict-dwarf -O2 -fPIC -Wall -Wsign-compare -Wfloat-equal -Wpointer-arith -Wcast-align -Wno-non-virtual-dtor -c HandleImpl.cpp -o HandleImpl.o
g++ -MD -MP -gstrict-dwarf -O2 -fPIC -Wall -Wsign-compare -Wfloat-equal -Wpointer-arith -Wcast-align -Wno-non-virtual-dtor -c ConsumerImpl.cpp -o ConsumerImpl.o
g++ -MD -MP -gstrict-dwarf -O2 -fPIC -Wall -Wsign-compare -Wfloat-equal -Wpointer-arith -Wcast-align -Wno-non-virtual-dtor -c ProducerImpl.cpp -o ProducerImpl.o
g++ -MD -MP -gstrict-dwarf -O2 -fPIC -Wall -Wsign-compare -Wfloat-equal -Wpointer-arith -Wcast-align -Wno-non-virtual-dtor -c KafkaConsumerImpl.cpp -o KafkaConsumerImpl.o
g++ -MD -MP -gstrict-dwarf -O2 -fPIC -Wall -Wsign-compare -Wfloat-equal -Wpointer-arith -Wcast-align -Wno-non-virtual-dtor -c TopicImpl.cpp -o TopicImpl.o
g++ -MD -MP -gstrict-dwarf -O2 -fPIC -Wall -Wsign-compare -Wfloat-equal -Wpointer-arith -Wcast-align -Wno-non-virtual-dtor -c TopicPartitionImpl.cpp -o TopicPartitionImpl.o
g++ -MD -MP -gstrict-dwarf -O2 -fPIC -Wall -Wsign-compare -Wfloat-equal -Wpointer-arith -Wcast-align -Wno-non-virtual-dtor -c MessageImpl.cpp -o MessageImpl.o
g++ -MD -MP -gstrict-dwarf -O2 -fPIC -Wall -Wsign-compare -Wfloat-equal -Wpointer-arith -Wcast-align -Wno-non-virtual-dtor -c HeadersImpl.cpp -o HeadersImpl.o
g++ -MD -MP -gstrict-dwarf -O2 -fPIC -Wall -Wsign-compare -Wfloat-equal -Wpointer-arith -Wcast-align -Wno-non-virtual-dtor -c QueueImpl.cpp -o QueueImpl.o
g++ -MD -MP -gstrict-dwarf -O2 -fPIC -Wall -Wsign-compare -Wfloat-equal -Wpointer-arith -Wcast-align -Wno-non-virtual-dtor -c MetadataImpl.cpp -o MetadataImpl.o
Generating pkg-config file rdkafka++.pc
Generating pkg-config file rdkafka++-static.pc
Creating shared library librdkafka++.1.dylib
Creating static library librdkafka++.a
g++  -shared -dynamiclib -Wl,-install_name,/usr/local/lib/librdkafka++.1.dylib RdKafka.o ConfImpl.o HandleImpl.o ConsumerImpl.o ProducerImpl.o KafkaConsumerImpl.o TopicImpl.o TopicPartitionImpl.o MessageImpl.o HeadersImpl.o QueueImpl.o MetadataImpl.o -o librdkafka++.1.dylib -L../src -lrdkafka
ar rcs librdkafka++.a RdKafka.o ConfImpl.o HandleImpl.o ConsumerImpl.o ProducerImpl.o KafkaConsumerImpl.o TopicImpl.o TopicPartitionImpl.o MessageImpl.o HeadersImpl.o QueueImpl.o MetadataImpl.o
cp librdkafka++.a librdkafka++-dbg.a
cp librdkafka++.1.dylib librdkafka++-dbg.1.dylib
Creating librdkafka++.dylib symlink
rm -f "librdkafka++.dylib" && ln -s "librdkafka++.1.dylib" "librdkafka++.dylib"
Checking librdkafka++ integrity
librdkafka++.1.dylib           OK
librdkafka++.a                 OK
cargo:rustc-link-search=native=/Users/<USER>/Desktop/logger/target/debug/build/rdkafka-sys-a6258ccbc7d8c245/out/src
cargo:rustc-link-lib=static=rdkafka
cargo:root=/Users/<USER>/Desktop/logger/target/debug/build/rdkafka-sys-a6258ccbc7d8c245/out
