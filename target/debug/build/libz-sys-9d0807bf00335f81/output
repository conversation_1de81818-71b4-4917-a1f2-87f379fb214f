cargo:rerun-if-env-changed=LIBZ_SYS_STATIC
cargo:rerun-if-changed=build.rs
cargo:rerun-if-changed=zng/cmake.rs
cargo:rerun-if-changed=zng/cc.rs
cargo:rerun-if-env-changed=ZLIB_NO_PKG_CONFIG
cargo:rerun-if-env-changed=PKG_CONFIG_x86_64-apple-darwin
cargo:rerun-if-env-changed=PKG_CONFIG_x86_64_apple_darwin
cargo:rerun-if-env-changed=HOST_PKG_CONFIG
cargo:rerun-if-env-changed=PKG_CONFIG
cargo:rerun-if-env-changed=ZLIB_STATIC
cargo:rerun-if-env-changed=ZLIB_DYNAMIC
cargo:rerun-if-env-changed=PKG_CONFIG_ALL_STATIC
cargo:rerun-if-env-changed=PKG_CONFIG_ALL_DYNAMIC
cargo:rerun-if-env-changed=PKG_CONFIG_PATH_x86_64-apple-darwin
cargo:rerun-if-env-changed=PKG_CONFIG_PATH_x86_64_apple_darwin
cargo:rerun-if-env-changed=HOST_PKG_CONFIG_PATH
cargo:rerun-if-env-changed=PKG_CONFIG_PATH
cargo:rerun-if-env-changed=PKG_CONFIG_LIBDIR_x86_64-apple-darwin
cargo:rerun-if-env-changed=PKG_CONFIG_LIBDIR_x86_64_apple_darwin
cargo:rerun-if-env-changed=HOST_PKG_CONFIG_LIBDIR
cargo:rerun-if-env-changed=PKG_CONFIG_LIBDIR
cargo:rerun-if-env-changed=PKG_CONFIG_SYSROOT_DIR_x86_64-apple-darwin
cargo:rerun-if-env-changed=PKG_CONFIG_SYSROOT_DIR_x86_64_apple_darwin
cargo:rerun-if-env-changed=HOST_PKG_CONFIG_SYSROOT_DIR
cargo:rerun-if-env-changed=PKG_CONFIG_SYSROOT_DIR
cargo:rerun-if-env-changed=ZLIB_STATIC
cargo:rerun-if-env-changed=ZLIB_DYNAMIC
cargo:rerun-if-env-changed=PKG_CONFIG_ALL_STATIC
cargo:rerun-if-env-changed=PKG_CONFIG_ALL_DYNAMIC
cargo:rustc-link-lib=z
cargo:rerun-if-env-changed=PKG_CONFIG_x86_64-apple-darwin
cargo:rerun-if-env-changed=PKG_CONFIG_x86_64_apple_darwin
cargo:rerun-if-env-changed=HOST_PKG_CONFIG
cargo:rerun-if-env-changed=PKG_CONFIG
cargo:rerun-if-env-changed=ZLIB_STATIC
cargo:rerun-if-env-changed=ZLIB_DYNAMIC
cargo:rerun-if-env-changed=PKG_CONFIG_ALL_STATIC
cargo:rerun-if-env-changed=PKG_CONFIG_ALL_DYNAMIC
cargo:rerun-if-env-changed=PKG_CONFIG_PATH_x86_64-apple-darwin
cargo:rerun-if-env-changed=PKG_CONFIG_PATH_x86_64_apple_darwin
cargo:rerun-if-env-changed=HOST_PKG_CONFIG_PATH
cargo:rerun-if-env-changed=PKG_CONFIG_PATH
cargo:rerun-if-env-changed=PKG_CONFIG_LIBDIR_x86_64-apple-darwin
cargo:rerun-if-env-changed=PKG_CONFIG_LIBDIR_x86_64_apple_darwin
cargo:rerun-if-env-changed=HOST_PKG_CONFIG_LIBDIR
cargo:rerun-if-env-changed=PKG_CONFIG_LIBDIR
cargo:rerun-if-env-changed=PKG_CONFIG_SYSROOT_DIR_x86_64-apple-darwin
cargo:rerun-if-env-changed=PKG_CONFIG_SYSROOT_DIR_x86_64_apple_darwin
cargo:rerun-if-env-changed=HOST_PKG_CONFIG_SYSROOT_DIR
cargo:rerun-if-env-changed=PKG_CONFIG_SYSROOT_DIR
OPT_LEVEL = Some(0)
OUT_DIR = Some(/Users/<USER>/Desktop/logger/target/debug/build/libz-sys-9d0807bf00335f81/out)
TARGET = Some(x86_64-apple-darwin)
HOST = Some(x86_64-apple-darwin)
cargo:rerun-if-env-changed=CC_x86_64-apple-darwin
CC_x86_64-apple-darwin = None
cargo:rerun-if-env-changed=CC_x86_64_apple_darwin
CC_x86_64_apple_darwin = None
cargo:rerun-if-env-changed=HOST_CC
HOST_CC = None
cargo:rerun-if-env-changed=CC
CC = None
cargo:rerun-if-env-changed=CC_ENABLE_DEBUG_OUTPUT
RUSTC_WRAPPER = None
cargo:rerun-if-env-changed=CRATE_CC_NO_DEFAULTS
CRATE_CC_NO_DEFAULTS = None
DEBUG = Some(true)
cargo:rerun-if-env-changed=MACOSX_DEPLOYMENT_TARGET
MACOSX_DEPLOYMENT_TARGET = None
cargo:rerun-if-env-changed=CFLAGS
CFLAGS = None
cargo:rerun-if-env-changed=HOST_CFLAGS
HOST_CFLAGS = None
cargo:rerun-if-env-changed=CFLAGS_x86_64_apple_darwin
CFLAGS_x86_64_apple_darwin = None
cargo:rerun-if-env-changed=CFLAGS_x86_64-apple-darwin
CFLAGS_x86_64-apple-darwin = None
CARGO_ENCODED_RUSTFLAGS = Some()
running LC_ALL="C" "cc" "-O0" "-ffunction-sections" "-fdata-sections" "-fPIC" "-gdwarf-2" "-fno-omit-frame-pointer" "-m64" "--target=x86_64-apple-macosx" "-mmacosx-version-min=15.5" "-Wall" "-Wextra" "src/smoke.c" "-g0" "-o" "/dev/null" "-lz"
cargo:rustc-link-lib=z
